@echo off
REM Pre-save hook: Validate JSON state files before saving

REM Check if the file being saved is a JSON file
if /i "%~x1"==".json" (
    echo Validating JSON file: %1
    
    REM Use Python to validate JSON syntax
    python -c "import json; json.load(open(r'%1', 'r', encoding='utf-8'))" 2>nul
    if errorlevel 1 (
        echo Error: Invalid JSON syntax in %1
        echo JSON validation failed - file will not be saved
        exit /b 1
    )
    echo JSON file validation successful
)