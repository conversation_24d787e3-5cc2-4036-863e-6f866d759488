@echo off
REM Session-end hook: Run unit tests when stopping a session

echo Running unit tests before session ends...

REM Check if pytest is available and run tests
python -m pytest --version >nul 2>&1
if errorlevel 1 (
    echo pytest not found, trying unittest discovery...
    python -m unittest discover -s . -p "test_*.py" -v
    if errorlevel 1 (
        echo Warning: Unit tests failed or no test framework found
        exit /b 1
    )
) else (
    echo Running pytest...
    python -m pytest -v
    if errorlevel 1 (
        echo Warning: Unit tests failed
        exit /b 1
    )
)

echo Unit tests completed successfully