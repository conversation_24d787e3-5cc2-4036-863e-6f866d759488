{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(del test_youtube_url.py)", "Bash(del test_ui.py)", "<PERSON><PERSON>(python:*)", "Bash(E:karaoke_appkaraoke_envScriptsactivate.bat)", "Bash(./karaoke_env/Scripts/activate)", "<PERSON><PERSON>(rmdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(black:*)", "Bash(.karaoke_envScriptsactivate)", "Bash(cmd /c:*)", "Bash(E:karaoke_appkaraoke_envScriptspython.exe -m pip install black)", "Bash(grep:*)"], "deny": []}}