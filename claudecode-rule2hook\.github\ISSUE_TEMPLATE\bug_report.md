---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Run command '...'
2. Enter rule '...'
3. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Generated Hook**
If applicable, paste the generated hook configuration:
```json
// Paste hook configuration here
```

**Environment:**
 - OS: [e.g. macOS, Linux, Windows]
 - Claude Code Version: [e.g. 0.3.0]
 - Shell: [e.g. bash, zsh]

**Additional context**
Add any other context about the problem here.