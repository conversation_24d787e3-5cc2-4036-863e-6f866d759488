name: Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  validate:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Validate JSON in examples
      run: |
        python -m json.tool .claude/commands/rule2hook.md > /dev/null || echo "Not a JSON file, skipping validation"
        
    - name: Check file permissions
      run: |
        test -x quick-test.sh || (echo "quick-test.sh is not executable" && exit 1)
        test -x validate-hooks.py || (echo "validate-hooks.py is not executable" && exit 1)
    
    - name: Validate hook examples
      run: |
        echo '{"hooks": {}}' > test_hooks.json
        python3 validate-hooks.py test_hooks.json