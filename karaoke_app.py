"""
🎤 Advanced Karaoke Studio - UVR Edition

A comprehensive karaoke application built with PyQt6 that provides:
- YouTube video download and processing
- High-quality vocal separation using UVR (Ultimate Vocal Remover) models
- Real-time audio effects (pitch shifting, volume control)
- Video/audio remuxing for karaoke playback
- Song library management with queue system
- Modern UI with light/dark themes

Dependencies:
- PyQt6: GUI framework
- librosa: Audio analysis and processing
- soundfile: Audio file I/O
- sounddevice: Real-time audio streaming
- audio-separator: UVR model integration
- pyrubberband: Real-time pitch shifting
- yt-dlp: YouTube video downloading
- ffmpeg: Audio/video processing

Author: Advanced Karaoke Studio Team
Version: 1.2.0
License: MIT
"""

import sys
import os
import subprocess
import hashlib
import json
import shutil
import tempfile
import signal
import random
import re
import time

try:
    import numpy as np
    import librosa
    import soundfile as sf
    import sounddevice as sd
    from audio_separator.separator import Separator
    import pyrubberband as pyrb
except ImportError as e:
    print(f"Missing required package: {e}")
    print(
        "Please install missing packages with: pip install librosa soundfile sounddevice audio-separator pyrubberband"
    )
    sys.exit(1)
from PyQt6.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QTextEdit,
    QFileDialog,
    QSlider,
    QGroupBox,
    QListWidget,
    QListWidgetItem,
    QSplitter,
    QSizePolicy,
    QMessageBox,
    QToolBar,
    QProgressBar,
    QComboBox,
    QCheckBox,
    QSpinBox,
    QTabWidget,
    QStyleFactory,
    QFrame,
    QGridLayout,
    QMenu,
    QProgressDialog,
    QStyle,
    QAbstractItemView,
)
from PyQt6.QtCore import (
    Qt,
    QThread,
    pyqtSignal,
    QTimer,
    QUrl,
    QObject,
    QPropertyAnimation,
    QEasingCurve,
    QMimeData,
)
from PyQt6.QtGui import (
    QShortcut,
    QKeySequence,
    QAction,
    QFont,
    QPalette,
    QColor,
    QIcon,
    QDrag,
    QPixmap,
    QPainter,
    QPen,
)
from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput
from PyQt6.QtMultimediaWidgets import QVideoWidget
from mutagen import File as MutagenFile

# --- Configuration and Setup ---
"""
Configuration constants and directory setup for the karaoke application.
These define the core file structure and storage locations for the app.
"""
LIBRARY_DIR = "karaoke_library"  # Main directory for storing processed songs
METADATA_FILE = os.path.join(LIBRARY_DIR, "library_metadata.json")  # Song metadata storage
MIXED_DIR = os.path.join(LIBRARY_DIR, "mixed")  # Temporary mixed audio files
REMUX_DIR = os.path.join(LIBRARY_DIR, "remux")  # Final karaoke video files

# Ensure required directories exist
os.makedirs(LIBRARY_DIR, exist_ok=True)
os.makedirs(MIXED_DIR, exist_ok=True)
os.makedirs(REMUX_DIR, exist_ok=True)

PREFERENCES_FILE = "user_preferences.json"  # User settings and preferences


def sanitize_filename(filename):
    """
    Sanitize a string to be safe for use as a filename across different operating systems.
    
    This function ensures that filenames are compatible with Windows, macOS, and Linux
    by removing or replacing problematic characters and adhering to length limits.
    
    Args:
        filename (str): The original filename string to sanitize
        
    Returns:
        str: A sanitized filename safe for filesystem use
        
    Note:
        - Removes characters that are invalid in Windows: < > : " / \\ | ? * & 
        - Replaces control characters (newlines, tabs) with spaces
        - Removes leading/trailing whitespace and dots
        - Collapses multiple spaces/underscores into single ones
        - Limits length to 80 characters to prevent path length issues
        - Ensures non-empty result (defaults to "untitled")
    """
    # Remove or replace characters that are not allowed in filenames
    invalid_chars = '<>:"/\\|?*"&'
    for char in invalid_chars:
        filename = filename.replace(char, "_")

    # Replace control characters with spaces for readability
    filename = filename.replace("\n", " ")
    filename = filename.replace("\r", " ")
    filename = filename.replace("\t", " ")

    # Remove leading/trailing whitespace and dots (dots can cause issues on some systems)
    filename = filename.strip(" .")

    # Collapse multiple consecutive spaces and underscores for cleaner filenames
    while "  " in filename:
        filename = filename.replace("  ", " ")
    while "__" in filename:
        filename = filename.replace("__", "_")

    # Limit length to prevent filesystem path length issues (especially on Windows)
    if len(filename) > 80:
        filename = filename[:80]

    # Ensure we never return an empty filename
    if not filename:
        filename = "untitled"

    return filename


def get_song_directory(unique_id, title=None):
    """
    Get the organized directory path for a song using the song title as the folder name.

    This creates a user-friendly directory structure where each song gets its own
    folder named after the song title, with a unique ID suffix to prevent conflicts.

    Args:
        unique_id (str): The unique identifier for the song (usually YouTube video ID)
        title (str, optional): The song title to use. If not provided, loads from metadata.

    Returns:
        str: The full path to the song's directory

    Example:
        For a song "My Favorite Song" with ID "abc123456789":
        Returns: "karaoke_library/My_Favorite_Song_abc12345"
    """
    if title is None:
        metadata = load_metadata()
        title = metadata.get(unique_id, {}).get("title", "Unknown Title")
    sanitized_title = sanitize_filename(title)
    # Use first 8 chars of ID to ensure uniqueness while keeping names readable
    folder_name = f"{sanitized_title}_{unique_id[:8]}"
    return os.path.join(LIBRARY_DIR, folder_name)


def get_song_files(unique_id):
    """
    Get all file paths for a song in the organized structure using the song title.
    
    This function returns a dictionary containing paths to all files associated
    with a processed song, making it easy to access any component file.
    
    Args:
        unique_id (str): The unique identifier for the song
        
    Returns:
        dict: Dictionary containing paths for all song-related files:
            - dir: Song directory path
            - video: Original video file (.mp4)
            - audio: Extracted audio file (.wav)
            - vocals: Separated vocals track (.wav)
            - instrumental: Separated instrumental track (.wav)
            - mixed: User-mixed audio (.wav)
            - remux: Final karaoke video (.mp4)
            - info: Video metadata (.json)
    """
    song_dir = get_song_directory(unique_id)
    metadata = load_metadata()
    title = metadata.get(unique_id, {}).get("title", "Unknown Title")
    sanitized_title = sanitize_filename(title)

    return {
        "dir": song_dir,
        "video": os.path.join(song_dir, f"{sanitized_title}.mp4"),
        "audio": os.path.join(song_dir, f"{sanitized_title}_audio.wav"),
        "vocals": os.path.join(song_dir, "vocals.wav"),
        "instrumental": os.path.join(song_dir, "instrumental.wav"),
        "mixed": os.path.join(song_dir, "mixed.wav"),
        "remux": os.path.join(song_dir, "karaoke.mp4"),
        "info": os.path.join(song_dir, f"{sanitized_title}.info.json"),
    }


def get_song_files_with_title(unique_id, title):
    """
    Get all file paths for a song using a provided title (avoids metadata dependency).

    This is used during the download process when we have the title but haven't
    saved the metadata yet, preventing circular dependencies.

    Args:
        unique_id (str): The unique identifier for the song
        title (str): The song title to use for file naming

    Returns:
        dict: Same structure as get_song_files(), but uses provided title
    """
    song_dir = get_song_directory(unique_id, title)  # Pass title to avoid "Unknown Title"
    sanitized_title = sanitize_filename(title)

    return {
        "dir": song_dir,
        "video": os.path.join(song_dir, f"{sanitized_title}.mp4"),
        "audio": os.path.join(song_dir, f"{sanitized_title}_audio.wav"),
        "vocals": os.path.join(song_dir, "vocals.wav"),
        "instrumental": os.path.join(song_dir, "instrumental.wav"),
        "mixed": os.path.join(song_dir, "mixed.wav"),
        "remux": os.path.join(song_dir, "karaoke.mp4"),
        "info": os.path.join(song_dir, f"{sanitized_title}.info.json"),
    }


# --- Theme Management System ---
"""
Comprehensive theming system supporting light and dark modes.
The theme system provides consistent colors and styling across the entire application.
"""

# Theme color definitions with semantic naming for easy customization
THEMES = {
    "dark": {
        # Base colors for dark theme (Discord-inspired)
        "bg_base": "#2c2f33",          # Main background
        "bg_secondary": "#23272a",      # Secondary panels
        "bg_tertiary": "#40444b",       # Input fields, sliders
        "text_primary": "#ffffff",      # Primary text
        "text_secondary": "#b9bbbe",    # Secondary text, labels
        "text_accent": "#7289da",       # Accent text, headers
        "accent": "#7289da",           # Primary accent color
        "accent_hover": "#8a9dfc",     # Hover state
        "accent_pressed": "#5f73bc",   # Pressed state
        "border": "#40444b",           # Borders, separators
        "success": "#43b581",          # Success indicators
        "warning": "#faa61a",          # Warning indicators
        "danger": "#f04747",           # Error indicators
        "icon_color": "white",         # Icon tint color
        "icon_folder": "dark",         # Icon theme variant
    },
    "light": {
        # Base colors for light theme (modern light UI)
        "bg_base": "#f2f3f5",          # Main background
        "bg_secondary": "#ffffff",      # Secondary panels
        "bg_tertiary": "#e3e5e8",       # Input fields, sliders
        "text_primary": "#060607",      # Primary text
        "text_secondary": "#4f5660",    # Secondary text, labels
        "text_accent": "#5865f2",       # Accent text, headers
        "accent": "#5865f2",           # Primary accent color
        "accent_hover": "#7983f3",     # Hover state
        "accent_pressed": "#4752c4",   # Pressed state
        "border": "#e3e5e8",           # Borders, separators
        "success": "#248046",          # Success indicators
        "warning": "#9f6d00",          # Warning indicators
        "danger": "#d83c3e",           # Error indicators
        "icon_color": "black",         # Icon tint color
        "icon_folder": "light",        # Icon theme variant
    },
}


def get_theme_stylesheet(theme_name="dark"):
    """
    Generate a comprehensive CSS stylesheet for the application based on the selected theme.
    
    This function creates a complete Qt stylesheet that styles all UI components
    consistently according to the chosen theme (light or dark).
    
    Args:
        theme_name (str): The theme to use ("dark" or "light")
        
    Returns:
        str: Complete CSS stylesheet for Qt widgets
        
    Note:
        The stylesheet covers all major widget types including:
        - Windows and panels
        - Buttons and inputs
        - Sliders and progress bars
        - Lists and menus
        - Custom widget styling
    """
    theme = THEMES.get(theme_name, THEMES["dark"])

    return f"""
        QWidget {{
            color: {theme['text_primary']};
            background-color: {theme['bg_base']};
            font-family: "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;
            font-size: 11pt;
        }}
        QFrame#main_frame {{
            border: 1px solid {theme['border']};
            border-radius: 8px;
        }}
        QGroupBox {{
            background-color: {theme['bg_secondary']};
            border: 1px solid {theme['border']};
            border-radius: 8px;
            margin-top: 10px;
            font-weight: bold;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 0 5px;
            color: {theme['text_accent']};
        }}
        QLabel, QCheckBox {{
            background-color: transparent;
        }}
        QLabel#title_label {{
            font-size: 16pt;
            font-weight: bold;
            color: {theme['text_primary']};
        }}
        QLabel#key_label, QLabel#bpm_label, QLabel#duration_label {{
            color: {theme['text_secondary']};
            font-size: 10pt;
        }}
        QLineEdit, QTextEdit {{
            background-color: {theme['bg_tertiary']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            padding: 5px;
            color: {theme['text_primary']};
        }}
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {theme['accent']};
        }}
        QPushButton {{
            background-color: {theme['accent']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: {theme['accent_hover']};
        }}
        QPushButton:pressed {{
            background-color: {theme['accent_pressed']};
        }}
        QPushButton:disabled {{
            background-color: {theme['bg_tertiary']};
            color: {theme['text_secondary']};
        }}
        QPushButton#theme_button {{
            background-color: transparent;
            border: none;
            padding: 5px;
        }}
        QSlider::groove:horizontal {{
            background: {theme['bg_tertiary']};
            height: 6px;
            border-radius: 3px;
        }}
        QSlider::handle:horizontal {{
            background: {theme['accent']};
            width: 16px;
            height: 16px;
            border-radius: 8px;
            margin: -5px 0;
        }}
        QListWidget {{
            background-color: {theme['bg_secondary']};
            border: 1px solid {theme['border']};
            border-radius: 8px;
        }}
        QListWidget::item {{
            padding: 8px;
        }}
        QListWidget::item:selected {{
            background-color: {theme['accent']};
            color: white;
        }}
        QListWidget::item:hover:!selected {{
            background-color: {theme['bg_tertiary']};
        }}
        QSplitter::handle {{
            background-color: {theme['border']};
        }}
        QSplitter::handle:horizontal {{
            width: 1px;
        }}
        QSplitter::handle:vertical {{
            height: 1px;
        }}
        QProgressBar {{
            border: 1px solid {theme['border']};
            border-radius: 4px;
            text-align: center;
            background-color: {theme['bg_tertiary']};
        }}
        QProgressBar::chunk {{
            background-color: {theme['success']};
            border-radius: 3px;
        }}
        QMenu {{
            background-color: {theme['bg_secondary']};
            border: 1px solid {theme['border']};
        }}
        QMenu::item:selected {{
            background-color: {theme['accent']};
            color: white;
        }}
    """


def apply_theme(app, theme_name):
    """
    Apply a theme to the entire application.
    
    This function sets the application-wide stylesheet and stores theme
    information as dynamic properties for easy access by widgets.
    
    Args:
        app (QApplication): The application instance
        theme_name (str): The theme to apply ("dark" or "light")
    """
    # Generate and apply the complete stylesheet
    stylesheet = get_theme_stylesheet(theme_name)
    app.setStyleSheet(stylesheet)

    # Store theme information as dynamic properties for widget access
    app.setProperty("theme_name", theme_name)
    app.setProperty("theme_colors", THEMES.get(theme_name, THEMES["dark"]))


# --- Core Processing Functions ---
"""
Core functions for video processing, audio analysis, and YouTube integration.
These functions handle the main workflow of downloading, analyzing, and processing songs.
"""

def get_video_id(url):
    """
    Extract the YouTube video ID from any YouTube URL format.
    
    Supports various YouTube URL formats including:
    - Standard watch URLs (youtube.com/watch?v=...)
    - Short URLs (youtu.be/...)
    - Embed URLs (youtube.com/embed/...)
    - Mobile URLs (m.youtube.com/...)
    - Shorts URLs (youtube.com/shorts/...)
    - Playlist URLs (extracts video ID from v parameter)
    
    Args:
        url (str): YouTube URL in any supported format
        
    Returns:
        str or None: 11-character YouTube video ID, or None if invalid
        
    Example:
        get_video_id("https://www.youtube.com/watch?v=dQw4w9WgXcQ") 
        Returns: "dQw4w9WgXcQ"
    """
    from urllib.parse import urlparse, parse_qs

    # Handle playlist URLs by extracting the video ID from the v parameter
    if "list=" in url and "v=" in url:
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            if "v" in query_params:
                video_id = query_params["v"][0]
                if len(video_id) == 11 and re.match(r"^[a-zA-Z0-9_-]{11}$", video_id):
                    return video_id
        except Exception:
            pass

    # YouTube video ID regex patterns for different URL formats
    patterns = [
        r"(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|m\.youtube\.com\/watch\?v=|youtube\.com\/shorts\/)([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})",
        r"youtu\.be\/([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/embed\/([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/v\/([a-zA-Z0-9_-]{11})",
        r"m\.youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})",
    ]

    for pattern in patterns:
        match = re.search(pattern, url, re.IGNORECASE)
        if match:
            return match.group(1)

    # No fallback pattern - the above patterns are comprehensive enough
    # It's better to fail explicitly on strange URLs than proceed with incorrect IDs
    return None


def generate_unique_id(url):
    """
    Generate a unique identifier for a video based on its URL.
    
    For YouTube URLs, this extracts and returns the video ID directly.
    For other URLs or invalid YouTube URLs, it generates a hash-based ID.
    
    Args:
        url (str): The video URL to generate an ID for
        
    Returns:
        str: Unique identifier (YouTube video ID or 10-char hash)
    """
    video_id = get_video_id(url)
    if video_id:
        return video_id
    else:
        # Generate a hash-based ID for non-YouTube URLs
        return hashlib.md5(url.encode()).hexdigest()[:10]


def load_metadata():
    """
    Load song library metadata from the JSON file.
    
    Returns:
        dict: Dictionary containing song metadata, or empty dict if file doesn't exist
        
    Note:
        Handles file corruption gracefully by returning empty dict and logging errors
    """
    if os.path.exists(METADATA_FILE):
        try:
            with open(METADATA_FILE, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading metadata: {e}")
    return {}


def save_metadata(metadata):
    """
    Save song library metadata to the JSON file.
    
    Args:
        metadata (dict): The metadata dictionary to save
        
    Note:
        Uses indented JSON for human readability and handles write errors gracefully
    """
    try:
        with open(METADATA_FILE, "w") as f:
            json.dump(metadata, f, indent=4)
    except Exception as e:
        print(f"Error saving metadata: {e}")


def load_preferences():
    """
    Load user preferences from the JSON preferences file.
    
    Returns:
        dict: Dictionary containing user preferences, or empty dict if file doesn't exist
    """
    if os.path.exists(PREFERENCES_FILE):
        try:
            with open(PREFERENCES_FILE, "r") as f:
                return json.load(f)
        except Exception as exc:
            print(f"Error loading preferences: {exc}")
    return {}


def save_preferences(prefs):
    """
    Save user preferences to the JSON preferences file.
    
    Args:
        prefs (dict): The preferences dictionary to save
    """
    try:
        with open(PREFERENCES_FILE, "w") as f:
            json.dump(prefs, f, indent=4)
    except Exception as exc:
        print(f"Error saving preferences: {exc}")


def ffmpeg_extract_wav_from_video(video_file_path, wav_out_path, log_callback=None):
    """
    Extract audio track from an MP4 into a WAV using ffmpeg. Overwrites target.
    Returns True if successful, False otherwise.
    """
    if not os.path.exists(video_file_path):
        return False

    try:
        cmd = [
            "ffmpeg",
            "-y",
            "-i",
            video_file_path,
            "-vn",
            "-acodec",
            "pcm_s16le",
            "-ar",
            "44100",
            "-ac",
            "2",
            wav_out_path,
        ]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        # Check if the output file was created and has content
        if (
            result.returncode == 0
            and os.path.exists(wav_out_path)
            and os.path.getsize(wav_out_path) > 0
        ):
            return True
        else:
            if log_callback:
                log_callback.emit(f"FFmpeg failed: return code {result.returncode}")
                log_callback.emit(f"Stderr: {result.stderr}")
            return False
    except Exception as e:
        if log_callback:
            log_callback.emit(f"Exception during ffmpeg extraction: {e}")
        return False


def detect_key_and_bpm(audio_file, log_callback=None, cancel_check=None):
    """Detect the key and BPM of an audio file using librosa."""
    if log_callback:
        log_callback.emit("Analyzing audio key and tempo...")

    # Check for cancellation
    if cancel_check and cancel_check():
        return "Unknown", 0.0

    try:
        y, sr = librosa.load(audio_file, sr=None, duration=90.0)

        # Check for cancellation after loading
        if cancel_check and cancel_check():
            return "Unknown", 0.0

        # Detect tempo (BPM)
        tempo, _ = librosa.beat.beat_track(y=y, sr=sr)

        # Check for cancellation after tempo detection
        if cancel_check and cancel_check():
            return "Unknown", 0.0

        # Detect key using chroma features
        chroma = librosa.feature.chroma_stft(y=y, sr=sr)
        chroma_mean = np.mean(chroma, axis=1)
        key_idx = np.argmax(chroma_mean)

        # Map to key names
        keys = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        detected_key = keys[key_idx]

        # FIX: Correctly get scalar from tempo which can be a numpy array
        tempo_value = float(tempo.item()) if hasattr(tempo, "item") else float(tempo)

        if log_callback:
            log_callback.emit(f"Detected Key: {detected_key}, BPM: {tempo_value:.1f}")

        return detected_key, tempo_value
    except Exception as e:
        if log_callback:
            log_callback.emit(f"Error detecting key/BPM: {e}")
        return "Unknown", 0.0


def download_video(youtube_url, unique_id, log_callback=None):
    """Downloads single video, renames it based on title, and extracts audio."""
    if log_callback:
        log_callback.emit(f"Processing URL: {youtube_url}")

    video_id = get_video_id(youtube_url)
    clean_url = (
        f"https://www.youtube.com/watch?v={video_id}" if video_id else youtube_url
    )

    # Create a temporary download directory to avoid naming conflicts
    temp_dir = tempfile.mkdtemp(prefix="karaoke_dl_")
    download_template = os.path.join(temp_dir, f"{unique_id}.%(ext)s")
    info_json_path = os.path.join(temp_dir, f"{unique_id}.info.json")

    if log_callback:
        log_callback.emit(f"Downloading to temporary location: {temp_dir}")

    command = [
        sys.executable,
        "-m",
        "yt_dlp",
        "-f",
        "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
        "--merge-output-format",
        "mp4",
        "--no-playlist",
        "-I",
        "1",
        "--write-info-json",
        "-o",
        download_template,
        clean_url,
    ]

    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)

        downloaded_title = "Unknown Title"
        if os.path.exists(info_json_path):
            with open(info_json_path, "r", encoding="utf-8") as f:
                info_dict = json.load(f)
                downloaded_title = info_dict.get("title", downloaded_title)

        if log_callback:
            log_callback.emit(f"Download complete. Title: '{downloaded_title}'")

        # Now that we have the title, we can get the final path
        # We'll create the paths directly without saving metadata to avoid race conditions

        final_files = get_song_files_with_title(unique_id, downloaded_title)
        os.makedirs(final_files["dir"], exist_ok=True)

        temp_video_path = os.path.join(temp_dir, f"{unique_id}.mp4")

        # Move the downloaded video to its final destination with the correct name
        if os.path.exists(temp_video_path):
            shutil.move(temp_video_path, final_files["video"])
            if log_callback:
                log_callback.emit(f"Moved video to: {final_files['video']}")
        else:
            raise FileNotFoundError("Downloaded MP4 not found in temp directory.")

        # Extract audio from the final video file
        if not ffmpeg_extract_wav_from_video(
            final_files["video"], final_files["audio"], log_callback
        ):
            raise Exception("Failed to extract audio from video.")

        # Return the ID and title so the main thread can update the metadata
        return final_files["video"], final_files["audio"], downloaded_title, unique_id

    except subprocess.CalledProcessError as e:
        if log_callback:
            log_callback.emit(f"Error: Download failed with yt-dlp. Stderr: {e.stderr}")
        return None, None, None
    except Exception as e:
        if log_callback:
            log_callback.emit(f"Error during download/rename process: {e}")
        return None, None, None
    finally:
        # Clean up the temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)


def separate_vocals_uvr(
    input_file,
    unique_id,
    use_gpu,
    output_dir=None,
    model_name="UVR-MDX-NET-Inst_3.onnx",
    log_callback=None,
    cancel_check=None,
):
    """Separates vocals using Ultimate Vocal Remover (UVR) models for superior quality."""
    if log_callback:
        log_callback.emit(f"Separating vocals using UVR model: {model_name}...")
    # Check for cancellation
    if cancel_check and cancel_check():
        return None, None
    # Use organized file structure
    files = get_song_files(unique_id)
    vocals_path = files["vocals"]
    instrumental_path = files["instrumental"]
    audio_wav = files["audio"]
    # Ensure input audio exists, try extracting from video if missing
    if not os.path.exists(input_file):
        if log_callback:
            log_callback.emit(
                f"Audio not found at {input_file}. Attempting extraction from video..."
            )
        video_path = files["video"]
        if os.path.exists(video_path):
            if ffmpeg_extract_wav_from_video(video_path, audio_wav, log_callback):
                input_file = audio_wav
                if log_callback:
                    log_callback.emit(f"Successfully extracted audio to {audio_wav}")
            else:
                if log_callback:
                    log_callback.emit("Failed to extract audio from video file.")
                return None, None
        else:
            if log_callback:
                log_callback.emit(
                    "Video file missing too; cannot proceed with separation."
                )
            return None, None
    if os.path.exists(vocals_path) and os.path.exists(instrumental_path):
        if log_callback:
            log_callback.emit(
                f"UVR separated files already exist. Skipping separation."
            )
        return vocals_path, instrumental_path
    try:
        # Check for cancellation before starting separation
        if cancel_check and cancel_check():
            return None, None

        device = "cuda" if use_gpu else "cpu"
        if log_callback:
            log_callback.emit(f"Attempting separation on device: {device}")

        # Note: audio-separator library sets device through environment or config
        # The 'device' parameter is not supported in the constructor
        separator = Separator(
            output_dir=files["dir"],
            output_format="WAV",
            model_file_dir=os.path.join(os.getcwd(), "uvr_models"),
        )
        if log_callback:
            log_callback.emit(f"Loading UVR model: {model_name}")
        if cancel_check and cancel_check():
            return None, None
        separator.load_model(model_filename=model_name)
        if cancel_check and cancel_check():
            return None, None
        if log_callback:
            log_callback.emit("Starting vocal separation with UVR...")
        separation_result = separator.separate(input_file)
        if not separation_result or len(separation_result) < 2:
            raise Exception(
                "UVR separation returned no results. Check if the input file exists and is accessible."
            )
        primary_stem_output_path, secondary_stem_output_path = separation_result
        if cancel_check and cancel_check():
            try:
                if os.path.exists(primary_stem_output_path):
                    os.remove(primary_stem_output_path)
                if os.path.exists(secondary_stem_output_path):
                    os.remove(secondary_stem_output_path)
            except:
                pass
            return None, None
        if os.path.exists(primary_stem_output_path):
            shutil.move(primary_stem_output_path, instrumental_path)
        if os.path.exists(secondary_stem_output_path):
            shutil.move(secondary_stem_output_path, vocals_path)
        if log_callback:
            log_callback.emit("UVR separation complete!")
            log_callback.emit(f"Vocals: {vocals_path}")
            log_callback.emit(f"Instrumental: {instrumental_path}")
        return vocals_path, instrumental_path
    except Exception as e:
        if log_callback:
            log_callback.emit(f"UVR separation failed: {e}")

        # If cancelled, do not fall back.
        if cancel_check and cancel_check():
            return None, None

        if log_callback:
            log_callback.emit("Falling back to Demucs...")

        # The rest of the block remains the same
        if output_dir is None:
            output_dir = LIBRARY_DIR
        return separate_vocals_demucs(
            input_file, unique_id, output_dir, log_callback, cancel_check
        )


def separate_vocals_demucs(
    input_file, unique_id, output_dir=LIBRARY_DIR, log_callback=None, cancel_check=None
):
    """Fallback: Separates vocals using Demucs."""
    if log_callback:
        log_callback.emit(f"Separating vocals using Demucs for {input_file}...")
    if cancel_check and cancel_check():
        return None, None
    files = get_song_files(unique_id)
    vocals_path = files["vocals"]
    instrumental_path = files["instrumental"]
    audio_wav = files["audio"]
    # Ensure input audio exists, try extracting from video if missing
    if not os.path.exists(input_file):
        if log_callback:
            log_callback.emit(
                f"Audio not found at {input_file}. Attempting extraction from video..."
            )
        video_path = files["video"]
        if os.path.exists(video_path):
            if ffmpeg_extract_wav_from_video(video_path, audio_wav, log_callback):
                input_file = audio_wav
                if log_callback:
                    log_callback.emit(f"Successfully extracted audio to {audio_wav}")
            else:
                if log_callback:
                    log_callback.emit("Failed to extract audio from video file.")
                return None, None
        else:
            if log_callback:
                log_callback.emit(
                    "Video file missing too; cannot proceed with separation."
                )
            return None, None
    if os.path.exists(vocals_path) and os.path.exists(instrumental_path):
        if log_callback:
            log_callback.emit(
                f"Demucs separated files already exist. Skipping separation."
            )
        return vocals_path, instrumental_path
    else:
        if log_callback:
            log_callback.emit(f"Running Demucs separation...")
        if cancel_check and cancel_check():
            return None, None
        base_name_for_folder = os.path.splitext(os.path.basename(input_file))[0]
        demucs_folder = os.path.join(output_dir, "htdemucs", base_name_for_folder)
        demucs_vocals_path = os.path.join(demucs_folder, "vocals.wav")
        demucs_instrumental_path = os.path.join(demucs_folder, "no_vocals.wav")
        command = [
            sys.executable,
            "-m",
            "demucs",
            "-n",
            "htdemucs",
            "--two-stems",
            "vocals",
            "-o",
            output_dir,
            "--filename",
            "{track}/{stem}.{ext}",
            input_file,
        ]
        try:
            process = subprocess.Popen(
                command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True
            )
            for line in process.stdout:
                if cancel_check and cancel_check():
                    process.terminate()
                    return None, None
                if log_callback:
                    log_callback.emit(line.strip())
            process.wait()
            if process.returncode == 0:
                if log_callback:
                    log_callback.emit("Separation complete.")
                # If expected exact paths not found, search the folder for matches
                if not (
                    os.path.exists(demucs_vocals_path)
                    and os.path.exists(demucs_instrumental_path)
                ):
                    if os.path.isdir(demucs_folder):
                        try:
                            for name in os.listdir(demucs_folder):
                                lower = name.lower()
                                full = os.path.join(demucs_folder, name)
                                if os.path.isfile(full):
                                    if lower.endswith(
                                        "vocals.wav"
                                    ) and not os.path.exists(demucs_vocals_path):
                                        demucs_vocals_path = full
                                    if (
                                        lower.endswith("no_vocals.wav")
                                        or lower.endswith("accompaniment.wav")
                                    ) and not os.path.exists(demucs_instrumental_path):
                                        demucs_instrumental_path = full
                        except Exception:
                            pass
                if os.path.exists(demucs_vocals_path) and os.path.exists(
                    demucs_instrumental_path
                ):
                    os.makedirs(os.path.dirname(vocals_path), exist_ok=True)
                    shutil.move(demucs_vocals_path, vocals_path)
                    shutil.move(demucs_instrumental_path, instrumental_path)
                    if log_callback:
                        log_callback.emit(f"Vocals: {vocals_path}")
                        log_callback.emit(f"Instrumental: {instrumental_path}")
                    return vocals_path, instrumental_path
                else:
                    if log_callback:
                        log_callback.emit(
                            f"Error: Separated files not found at expected Demucs location."
                        )
                        log_callback.emit(f"Tried folder: {demucs_folder}")
                    return None, None
            else:
                if log_callback:
                    log_callback.emit(
                        f"Error: Separation failed with return code {process.returncode}"
                    )
                return None, None
        except Exception as e:
            if log_callback:
                log_callback.emit(f"Error separating vocals: {e}")
            return None, None


# --- Real-Time Audio Streaming ---
class RealTimeAudioStreamer(QObject):
    """Real-time audio streaming with low latency using sounddevice."""

    def __init__(self, log_callback=None):
        super().__init__()
        self.vocal_data = None
        self.instrumental_data = None
        self.sr = 44100
        self.current_frame = 0
        self.is_playing = False
        self.vocal_volume = 1.0
        self.instrumental_volume = 1.0
        self.vocal_pitch_shift = 0
        self.instrumental_pitch_shift = 0
        self.buffer_size = 1024
        self.stream = None
        self.total_frames = 0
        self.log_callback = log_callback if log_callback else (lambda msg: None) # Default to no-op

    def load_audio_files(self, vocals_path, instrumental_path):
        """Load vocal and instrumental audio files."""
        try:
            if not os.path.exists(vocals_path) or not os.path.exists(instrumental_path):
                self.log_callback(f"Audio files not found: {vocals_path}, {instrumental_path}")
                return False

            self.vocal_data, self.sr = librosa.load(vocals_path, sr=self.sr, mono=True)
            self.instrumental_data, _ = librosa.load(
                instrumental_path, sr=self.sr, mono=True
            )

            # Ensure both tracks have the same length
            min_len = min(len(self.vocal_data), len(self.instrumental_data))
            self.vocal_data = self.vocal_data[:min_len]
            self.instrumental_data = self.instrumental_data[:min_len]
            self.total_frames = min_len
            self.current_frame = 0
            self.log_callback(
                f"Real-time audio loaded: {min_len} frames, {self.total_frames/self.sr:.1f}s"
            )
            return True
        except Exception as e:
            self.log_callback(f"Error loading audio files: {e}")
            return False

    def audio_callback(self, outdata, frames, time, status):
        """Audio callback function for real-time playback."""
        if (
            not self.is_playing
            or self.vocal_data is None
            or self.instrumental_data is None
        ):
            outdata[:] = 0
            return

        if self.current_frame >= self.total_frames:
            outdata[:] = 0
            self.is_playing = False
            return

        # Calculate end frame for this chunk
        end_frame = min(self.current_frame + frames, self.total_frames)
        chunk_frames = end_frame - self.current_frame

        # Get audio chunks
        vocal_chunk = (
            self.vocal_data[self.current_frame : end_frame] * self.vocal_volume
        )
        instrumental_chunk = (
            self.instrumental_data[self.current_frame : end_frame]
            * self.instrumental_volume
        )

        # Apply real-time pitch shifting using pyrubberband (faster than librosa)
        # This provides "good enough" real-time pitch shifting with better performance
        try:
            if self.vocal_pitch_shift != 0 and len(vocal_chunk) > 0:
                # Convert semitones to frequency ratio
                pitch_ratio = 2.0 ** (self.vocal_pitch_shift / 12.0)
                vocal_chunk = pyrb.pitch_shift(vocal_chunk, self.sr, pitch_ratio)

            if self.instrumental_pitch_shift != 0 and len(instrumental_chunk) > 0:
                # Convert semitones to frequency ratio
                pitch_ratio = 2.0 ** (self.instrumental_pitch_shift / 12.0)
                instrumental_chunk = pyrb.pitch_shift(
                    instrumental_chunk, self.sr, pitch_ratio
                )
        except Exception as e:
            # If pitch shifting fails, continue without it to avoid audio dropouts
            self.log_callback(f"Real-time pitch shifting error: {e}")

        # Ensure chunks have the same length before mixing
        min_len = min(len(vocal_chunk), len(instrumental_chunk))
        vocal_chunk = vocal_chunk[:min_len]
        instrumental_chunk = instrumental_chunk[:min_len]

        # Mix audio
        mixed_chunk = vocal_chunk + instrumental_chunk

        # Ensure we don't clip
        mixed_chunk = np.clip(mixed_chunk, -1.0, 1.0)

        # Fill output buffer (stereo)
        if chunk_frames < frames:
            outdata[:chunk_frames, 0] = mixed_chunk
            outdata[:chunk_frames, 1] = mixed_chunk
            outdata[chunk_frames:] = 0
        else:
            outdata[:, 0] = mixed_chunk
            outdata[:, 1] = mixed_chunk

        self.current_frame = end_frame

    def start_playback(self):
        """Start real-time audio playback."""
        if self.vocal_data is None or self.instrumental_data is None:
            return False

        try:
            self.stream = sd.OutputStream(
                samplerate=self.sr,
                channels=2,
                callback=self.audio_callback,
                blocksize=self.buffer_size,
                latency="low",
            )
            self.stream.start()
            self.is_playing = True
            return True
        except Exception as e:
            print(f"Error starting audio stream: {e}")
            return False

    def pause_playback(self):
        """Pause audio playback."""
        self.is_playing = False

    def resume_playback(self):
        """Resume audio playback."""
        if self.stream is not None:
            self.is_playing = True

    def stop_playback(self):
        """Stop audio playback and clean up."""
        self.is_playing = False
        if self.stream is not None:
            self.stream.stop()
            self.stream.close()
            self.stream = None
        self.current_frame = 0

    def seek(self, position_seconds):
        """Seek to a specific position in the audio."""
        if self.total_frames > 0:
            self.current_frame = int(position_seconds * self.sr)
            self.current_frame = max(0, min(self.current_frame, self.total_frames))

    def set_volumes(self, vocal_volume, instrumental_volume):
        """Set real-time volume levels (0.0 to 2.0)."""
        self.vocal_volume = vocal_volume / 100.0
        self.instrumental_volume = instrumental_volume / 100.0

    def set_pitch_shifts(self, vocal_pitch, instrumental_pitch):
        """Set real-time pitch shifting levels (-12 to +12 semitones)."""
        self.vocal_pitch_shift = vocal_pitch
        self.instrumental_pitch_shift = instrumental_pitch

    def get_position(self):
        """Get current playback position in seconds."""
        if self.total_frames > 0:
            return self.current_frame / self.sr
        return 0.0

    def get_duration(self):
        """Get total duration in seconds."""
        if self.total_frames > 0:
            return self.total_frames / self.sr
        return 0.0


# --- Audio Mixing and Effects ---
def mix_and_apply_effects(
    vocals_path,
    instrumental_path,
    vocal_volume,
    instrumental_volume,
    vocal_pitch,
    instrumental_pitch,
    output_path,
    log_callback=None,
):
    """Loads audio, applies volume/pitch, mixes, and saves."""
    if log_callback:
        log_callback.emit("Loading audio files for mixing...")
    try:
        vocal_data, sr = librosa.load(vocals_path, sr=None, mono=True)
        instrumental_data, _ = librosa.load(instrumental_path, sr=sr, mono=True)
        
        if log_callback:
            log_callback.emit("Applying volume and pitch shift...")
        
        vocal_data = vocal_data * (vocal_volume / 100.0)
        instrumental_data = instrumental_data * (instrumental_volume / 100.0)
        
        if vocal_pitch != 0:
            if log_callback:
                log_callback.emit(f"Pitch shifting vocals by {vocal_pitch} semitones...")
            vocal_data = librosa.effects.pitch_shift(
                vocal_data, sr=sr, n_steps=vocal_pitch
            )
        if instrumental_pitch != 0:
            if log_callback:
                log_callback.emit(f"Pitch shifting instrumental by {instrumental_pitch} semitones...")
            instrumental_data = librosa.effects.pitch_shift(
                instrumental_data, sr=sr, n_steps=instrumental_pitch
            )
        
        if log_callback:
            log_callback.emit("Mixing audio tracks...")
        min_len = min(len(vocal_data), len(instrumental_data))
        vocal_data = vocal_data[:min_len]
        instrumental_data = instrumental_data[:min_len]
        mixed_data = vocal_data + instrumental_data
        
        if log_callback:
            log_callback.emit("Normalizing mixed audio...")
        # Normalize
        max_amp = np.max(np.abs(mixed_data))
        if max_amp > 1.0 and max_amp > 0:
            mixed_data = mixed_data / max_amp
        
        if log_callback:
            log_callback.emit("Saving mixed audio to WAV...")
        # Export as stereo by duplicating mono to two channels for better player support
        stereo = np.stack([mixed_data, mixed_data], axis=1)
        sf.write(output_path, stereo, sr)
        
        if log_callback:
            log_callback.emit(f"Mixed and saved to: {output_path}")
        return output_path
    except Exception as e:
        if log_callback:
            log_callback.emit(f"Error in mixing/effects: {e}")
        return None


def remux_video_with_audio(video_path, audio_wav_path, out_mp4_path, log_callback=None):
    """
    Remuxes original video with provided WAV audio into MP4 using ffmpeg, re-encoding audio to AAC.
    Video stream is copied to preserve quality and timing.
    Includes retry mechanism for Windows file locking issues.
    """
    max_retries = 3
    retry_delay = 0.5  # seconds

    # Validate input files exist before starting
    if not os.path.exists(video_path):
        if log_callback:
            log_callback.emit(f"Video file not found: {video_path}")
        return None
    if not os.path.exists(audio_wav_path):
        if log_callback:
            log_callback.emit(f"Audio file not found: {audio_wav_path}")
        return None

    for attempt in range(max_retries):
        if log_callback:
            if attempt == 0:
                log_callback.emit("Remuxing mixed audio with original video...")
            else:
                log_callback.emit(f"Retry attempt {attempt + 1}/{max_retries}...")

        try:
            cmd = [
                "ffmpeg",
                "-y",
                "-i",
                video_path,
                "-i",
                audio_wav_path,
                "-c:v",
                "copy",
                "-c:a",
                "aac",
                "-b:a",
                "192k",
                "-map",
                "0:v:0",
                "-map",
                "1:a:0",
                "-shortest",
                out_mp4_path,
            ]
            proc = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )

            if proc.returncode == 0:
                return out_mp4_path
            else:
                error_msg = proc.stderr
                # Check if it's a file locking issue
                if (
                    "Permission denied" in error_msg
                    or "Resource busy" in error_msg
                    or "cannot access" in error_msg.lower()
                ):
                    if attempt < max_retries - 1:
                        if log_callback:
                            log_callback.emit(
                                f"File locked, waiting {retry_delay}s before retry..."
                            )
                        time.sleep(retry_delay)
                        continue

                if log_callback:
                    log_callback.emit(f"ffmpeg remux failed: {error_msg}")
                return None

        except Exception as e:
            if log_callback:
                log_callback.emit(f"Remux error: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None

    # If we get here, all retries failed
    if log_callback:
        log_callback.emit(f"Remux failed after {max_retries} attempts")
    return None


# --- Media Playback with Crossfade ---
class CrossfadePlayer(QObject):
    """Wrapper around QMediaPlayer providing basic crossfade support."""

    positionChanged = pyqtSignal(int)
    durationChanged = pyqtSignal(int)
    playbackStateChanged = pyqtSignal(QMediaPlayer.PlaybackState)
    crossfadeFinished = pyqtSignal()

    def __init__(self, parent=None, fade_duration: int = 0):
        super().__init__(parent)
        self.fade_duration_ms = int(fade_duration * 1000)
        self.player1 = QMediaPlayer(parent)
        self.out1 = QAudioOutput(parent)
        self.player1.setAudioOutput(self.out1)
        self.player2 = QMediaPlayer(parent)
        self.out2 = QAudioOutput(parent)
        self.player2.setAudioOutput(self.out2)
        self.current_player = self.player1
        self.next_player = self.player2
        self.video_widget = None
        self.timer = QTimer(self)
        self.timer.setInterval(100)
        self.timer.timeout.connect(self._on_step)
        self._progress = 0.0
        # Only connect signals from the current player to avoid conflicts
        self.player1.positionChanged.connect(self._on_position_changed)
        self.player1.durationChanged.connect(self._on_duration_changed)
        self.player1.playbackStateChanged.connect(self._on_playback_state_changed)
        self.player2.positionChanged.connect(self._on_position_changed)
        self.player2.durationChanged.connect(self._on_duration_changed)
        self.player2.playbackStateChanged.connect(self._on_playback_state_changed)

    # basic passthrough API
    def setVideoOutput(self, widget):
        self.video_widget = widget
        self.player1.setVideoOutput(widget)

    def setSource(self, url: QUrl):
        self.current_player.setSource(url)

    def play(self):
        try:
            self.current_player.play()
        except Exception as e:
            print(f"Error in CrossfadePlayer.play(): {e}")

    def pause(self):
        try:
            self.current_player.pause()
        except Exception as e:
            print(f"Error in CrossfadePlayer.pause(): {e}")

    def stop(self):
        try:
            self.current_player.stop()
            # Also stop the timer if it's running
            if self.timer.isActive():
                self.timer.stop()
        except Exception as e:
            print(f"Error in CrossfadePlayer.stop(): {e}")

    def setPosition(self, pos: int):
        self.current_player.setPosition(pos)

    def playbackState(self):
        return self.current_player.playbackState()

    def position(self):
        return self.current_player.position()

    def duration(self):
        return self.current_player.duration()

    def setFadeDuration(self, seconds: int):
        self.fade_duration_ms = int(seconds * 1000)

    def crossfade_to(self, url: QUrl):
        if self.timer.isActive():
            return
        self.next_player.setSource(url)
        self.next_player.setVideoOutput(None)
        self.out2.setVolume(0.0)
        self.next_player.play()
        self._progress = 0.0
        self.timer.start()

    def _on_step(self):
        if self.fade_duration_ms <= 0:
            self.timer.stop()
            self.current_player.stop()
            self._swap()
            self.crossfadeFinished.emit()
            return
        step = self.timer.interval() / self.fade_duration_ms
        self._progress += step
        self.out1.setVolume(max(0.0, 1.0 - self._progress))
        self.out2.setVolume(min(1.0, self._progress))
        if self._progress >= 1.0:
            self.timer.stop()
            self.current_player.stop()
            self._swap()
            self.crossfadeFinished.emit()

    def _swap(self):
        self.current_player, self.next_player = self.next_player, self.current_player
        self.out1, self.out2 = self.out2, self.out1
        if self.video_widget:
            self.current_player.setVideoOutput(self.video_widget)
        self.out1.setVolume(1.0)
        self.next_player.setVideoOutput(None)
        self.out2.setVolume(1.0)

    def _on_position_changed(self, position):
        """Forward position changes only from the current player."""
        sender = self.sender()
        if sender == self.current_player:
            self.positionChanged.emit(position)

    def _on_duration_changed(self, duration):
        """Forward duration changes only from the current player."""
        sender = self.sender()
        if sender == self.current_player:
            self.durationChanged.emit(duration)

    def _on_playback_state_changed(self, state):
        """Forward playback state changes only from the current player."""
        sender = self.sender()
        if sender == self.current_player:
            self.playbackStateChanged.emit(state)


# --- Worker Threads ---
class ProcessingThread(QThread):
    log_message = pyqtSignal(str)
    progress_update = pyqtSignal(int, str)  # progress percentage, status message
    finished_processing = pyqtSignal(
        str, str, str, str, str, str, float, str
    )  # video_file, audio_file, vocals_file, instrumental_file, title, key, bmp, unique_id

    def __init__(
        self, youtube_url, separation_model="UVR-MDX-NET-Inst_3.onnx", use_gpu=False
    ):
        super().__init__()
        self.youtube_url = youtube_url
        self.unique_id = generate_unique_id(youtube_url)
        self.separation_model = separation_model
        self.use_gpu = use_gpu
        self.cancelled = False

    def cancel(self):
        """Cancel the processing operation."""
        self.cancelled = True
        self.log_message.emit("🚫 Processing cancelled by user")

    def run(self):
        try:
            if self.cancelled:
                return

            self.progress_update.emit(10, "Downloading video...")
            video_result = download_video(
                self.youtube_url, self.unique_id, log_callback=self.log_message
            )
            if self.cancelled:
                return

            if not video_result or video_result[0] is None:
                self.log_message.emit("Processing stopped: Download/check failed.")
                self.finished_processing.emit(
                    None, None, None, None, None, None, 0.0, None
                )
                return

            video_file, audio_file, title, unique_id = video_result

            if self.cancelled:
                return

            # Save metadata immediately after download to ensure correct directory naming
            metadata = load_metadata()
            metadata[unique_id] = {
                "title": title,
                "files": get_song_files_with_title(unique_id, title)
            }
            save_metadata(metadata)

            self.progress_update.emit(30, "Analyzing audio...")
            # Detect key and BPM
            key, bpm = detect_key_and_bpm(
                audio_file,
                log_callback=self.log_message,
                cancel_check=lambda: self.cancelled,
            )
            # Ensure we have valid values
            if key == "Unknown" or bpm == 0.0:
                self.log_message.emit("Warning: Key/BPM detection failed, using defaults.")
                key = "Unknown"
                bpm = 0.0

            if self.cancelled:
                return

            self.progress_update.emit(
                50, "Separating vocals (this may take several minutes)..."
            )
            # Use UVR for superior vocal separation
            vocals_file, instrumental_file = separate_vocals_uvr(
                audio_file,
                self.unique_id,
                self.use_gpu,
                model_name=self.separation_model,
                log_callback=self.log_message,
                cancel_check=lambda: self.cancelled,
            )

            if self.cancelled:
                return

            if not vocals_file or not instrumental_file:
                self.log_message.emit("Processing stopped: Separation/check failed.")
                self.finished_processing.emit(
                    video_file, audio_file, None, None, title, key, bpm, unique_id
                )
                return

            # Update metadata with analysis results
            metadata = load_metadata()
            if unique_id in metadata:
                metadata[unique_id].update({
                    "key": key,
                    "bpm": bpm,
                    "vocals_file": vocals_file,
                    "instrumental_file": instrumental_file
                })
                save_metadata(metadata)

            self.progress_update.emit(100, "Processing complete!")
            self.finished_processing.emit(
                video_file,
                audio_file,
                vocals_file,
                instrumental_file,
                title,
                key,
                bpm,
                unique_id,
            )
        except Exception as e:
            self.log_message.emit(f"Unexpected error in thread: {e}")
            self.finished_processing.emit(
                None, None, None, None, None, "Unknown", 0.0, None
            )


class MixingThread(QThread):
    log_message = pyqtSignal(str)
    finished_mixing = pyqtSignal(str, str)  # mixed_wav_path, remuxed_mp4_path

    def __init__(
        self,
        vocals_path,
        instrumental_path,
        vocal_vol,
        inst_vol,
        vocal_pitch,
        inst_pitch,
        output_wav_path,
        video_path,
        remux_out_path,
    ):
        super().__init__()
        self.vocals_path = vocals_path
        self.instrumental_path = instrumental_path
        self.vocal_vol = vocal_vol
        self.inst_vol = inst_vol
        self.vocal_pitch = vocal_pitch
        self.inst_pitch = inst_pitch
        self.output_wav_path = output_wav_path
        self.video_path = video_path
        self.remux_out_path = remux_out_path
        # playback state hints
        self.position_to_seek = 0.0
        self.was_playing = False

    def run(self):
        try:
            mixed_wav = mix_and_apply_effects(
                self.vocals_path,
                self.instrumental_path,
                self.vocal_vol,
                self.inst_vol,
                self.vocal_pitch,
                self.inst_pitch,
                self.output_wav_path,
                log_callback=self.log_message,
            )
            if not mixed_wav:
                self.finished_mixing.emit(None, None)
                return
            # Remux into MP4 for A/V sync
            remuxed = remux_video_with_audio(
                self.video_path,
                mixed_wav,
                self.remux_out_path,
                log_callback=self.log_message,
            )
            self.finished_mixing.emit(mixed_wav, remuxed)
        except Exception as e:
            self.log_message.emit(f"Unexpected error in mixing thread: {e}")
            self.finished_mixing.emit(None, None)


class LibraryListWidget(QListWidget):
    """Custom QListWidget that enables dragging items with unique_id data."""

    def __init__(self):
        super().__init__()
        self.setDragDropMode(QAbstractItemView.DragDropMode.DragOnly)
        self.setDefaultDropAction(Qt.DropAction.CopyAction)

    def startDrag(self, supportedActions):
        item = self.currentItem()
        if item:
            drag = QDrag(self)
            mimeData = QMimeData()

            # Get the unique_id from the item data
            data = item.data(Qt.ItemDataRole.UserRole)
            if data:
                if isinstance(data, tuple) and len(data) >= 4:
                    # Always expect the tuple format and extract the ID from the 4th position
                    unique_id = data[3]
                else:
                    # If the data is not in the expected format, we cannot proceed.
                    return

                mimeData.setText(str(unique_id))
                drag.setMimeData(mimeData)
                drag.exec(supportedActions)


class QueueListWidget(QListWidget):
    """Custom QListWidget that handles drag-drop from library to queue."""

    def __init__(self, parent_app):
        super().__init__()
        self.parent_app = parent_app
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DragDropMode.DropOnly)
        self.setDefaultDropAction(Qt.DropAction.CopyAction)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        if event.mimeData().hasText():
            try:
                unique_id = event.mimeData().text()
                self.parent_app.add_to_queue(unique_id)
                event.acceptProposedAction()
            except Exception as e:
                self.parent_app.log_output.append(f"Drop error: {e}")
                event.ignore()
        else:
            event.ignore()


# --- Main Application Window ---
class KaraokeApp(QWidget):
    def __init__(self):
        print("KaraokeApp: Starting initialization...", flush=True)
        super().__init__()
        print("KaraokeApp: QWidget init complete", flush=True)

        self.processing_thread = None
        self.mixing_thread = None
        print("KaraokeApp: Loading metadata...", flush=True)
        self.metadata = load_metadata()
        print("KaraokeApp: Metadata loaded", flush=True)

        self.current_mixed_wav = None
        self.current_remuxed_mp4 = None
        self.is_playing = False
        self.total_length_seconds = 0
        self.current_position_seconds = 0
        self.current_unique_id = None
        self.current_video_file = None
        self.current_vocals_file = None
        self.current_instrumental_file = None
        self.current_title = "No Song Selected"
        self.current_key = "Unknown"
        self.current_bpm = 0.0

        print("KaraokeApp: Loading preferences...", flush=True)
        self.preferences = load_preferences()
        self.fade_duration = self.preferences.get("fade_duration", 0)
        self.separation_model = self.preferences.get(
            "separation_model", "UVR-MDX-NET-Inst_3.onnx"
        )
        self.use_realtime_audio = self.preferences.get("use_realtime_audio", False)
        self.use_gpu = self.preferences.get("use_gpu", True)  # GPU enabled by default
        self.current_theme = self.preferences.get("theme", "dark")
        self.next_item = None
        self.crossfade_in_progress = False
        self.system_initiated_stop = (
            False  # Add flag to prevent unintended queue advance
        )

        # Initialize selection tracking and queue
        self.selected_songs = set()
        self.current_queue = []  # List of unique_ids
        self.current_queue_index = -1

        print("KaraokeApp: Preferences loaded", flush=True)

        # Real-time audio streamer - initialize after log_output is created
        self.realtime_streamer = None

        # For auto-mixing on slider change
        print("KaraokeApp: Setting up timers...", flush=True)
        self.slider_change_timer = QTimer(self)
        self.slider_change_timer.setSingleShot(True)
        self.slider_change_timer.timeout.connect(self.apply_effects_and_mix_auto)

        # Real-time position update timer
        self.realtime_position_timer = QTimer(self)
        self.realtime_position_timer.timeout.connect(self.update_realtime_position)
        print("KaraokeApp: Timers set up", flush=True)

        print("KaraokeApp: Starting UI initialization...", flush=True)
        self.initUI()
        print("KaraokeApp: UI initialization complete", flush=True)

        print("KaraokeApp: Loading library...", flush=True)
        try:
            self.load_library()
            print("KaraokeApp: Library loaded", flush=True)
        except Exception as e:
            print(f"KaraokeApp: Library loading failed: {e}", flush=True)
            self.metadata = {}

        print("KaraokeApp: Setting up playback timer...", flush=True)
        self.playback_timer = QTimer(self)
        self.playback_timer.timeout.connect(self.update_playback_position)
        print("KaraokeApp: Initialization complete!", flush=True)

    def _create_pixmap_from_text(self, icon_text, size, theme_colors):
        """Helper to create a QPixmap with a Unicode icon drawn on it."""
        pixmap = QPixmap(size[0], size[1])
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Fallback to default colors if theme_colors is None
        if theme_colors is None:
            theme_colors = THEMES["dark"]  # Use dark theme as fallback
        
        color = QColor(theme_colors.get("text_primary", "#FFFFFF"))
        painter.setPen(QPen(color))
        font = QFont()
        font.setPointSize(max(12, size[0] // 3))
        painter.setFont(font)

        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, icon_text)
        painter.end()
        return pixmap

    def create_icon_button(self, icon_text, size=(32, 32), tooltip=None):
        """Create a button with a Unicode icon instead of text."""
        button = QPushButton()
        button.setFixedSize(*size)

        app = QApplication.instance()
        theme_colors = app.property("theme_colors")
        pixmap = self._create_pixmap_from_text(icon_text, size, theme_colors)

        button.setIcon(QIcon(pixmap))
        button.setText("")  # Remove text

        if tooltip:
            button.setToolTip(tooltip)

        return button

    def clean_library_action(self):
        # Confirm destructive action
        reply = QMessageBox.question(
            self,
            "Confirm Clean Library",
            "This will permanently delete EVERYTHING inside 'karaoke_library' (including downloaded videos) and also remove the 'separated' folder. Continue?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )
        if reply != QMessageBox.StandardButton.Yes:
            return
        try:
            # Stop playback to release file handles
            try:
                self.media_player.stop()
            except Exception:
                pass

            # Also stop real-time audio streamer
            try:
                if hasattr(self, "realtime_streamer") and self.realtime_streamer:
                    self.realtime_streamer.stop_playback()
            except Exception:
                pass
            # Delete karaoke_library contents
            if os.path.exists(LIBRARY_DIR):
                for name in os.listdir(LIBRARY_DIR):
                    path = os.path.join(LIBRARY_DIR, name)
                    try:
                        if os.path.isdir(path):
                            shutil.rmtree(path, ignore_errors=True)
                        else:
                            try:
                                os.remove(path)
                            except Exception:
                                pass
                    except Exception:
                        pass
            # Recreate expected subfolders
            os.makedirs(MIXED_DIR, exist_ok=True)
            os.makedirs(REMUX_DIR, exist_ok=True)

            # Clear UI state and metadata
            self.metadata = {}
            save_metadata(self.metadata)
            self.library_list.clear()
            self.current_mixed_wav = None
            self.current_remuxed_mp4 = None
            self.current_video_file = None
            self.current_vocals_file = None
            self.current_instrumental_file = None
            self.current_unique_id = None
            self.current_title = "No Song Selected"
            self.current_position_seconds = 0
            self.total_length_seconds = 0
            self.play_button.setEnabled(False)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.track_position_slider.setValue(0)
            self.playback_position_label.setText("Position: 00:00 / 00:00")
            self.log_output.append("Library cleaned successfully.")
        except Exception as e:
            self.log_output.append(f"Error during cleanup: {e}")

    def initUI(self):
        print("    Setting window title and geometry...", flush=True)
        self.setWindowTitle("🎤 Advanced Karaoke Studio - UVR Edition")
        self.setGeometry(100, 100, 1400, 900)

        # Enable responsive resizing
        self.setMinimumSize(1200, 700)  # Minimum usable size
        print("    Window properties set...", flush=True)

        # Resize event will be handled by overriding resizeEvent method
        print("    Resize handler will be connected...", flush=True)

        # Theming is now handled by the apply_current_theme method
        print("    Stylesheet will be set by the theme manager.", flush=True)

        print("    Creating main layout...", flush=True)
        main_vlayout = QVBoxLayout()

        # Enhanced Toolbar with more options
        print("    Creating toolbar...", flush=True)
        toolbar = QToolBar("Actions", self)
        print("    Toolbar created...", flush=True)

        print("    Adding toolbar actions...", flush=True)
        btn_clean = QAction("🧹 Clean Library", self)
        btn_clean.triggered.connect(self.clean_library_action)
        toolbar.addAction(btn_clean)

        toolbar.addSeparator()

        # Separation model selector
        toolbar.addWidget(QLabel("AI Model:"))
        self.model_selector = QComboBox()
        self.model_selector.addItems(
            [
                "UVR-MDX-NET-Inst_3.onnx",
                "UVR-MDX-NET-Inst_Main",
                "MDX23C-8kHz-INST",
                "htdemucs (fallback)",
            ]
        )
        self.model_selector.setCurrentText(self.separation_model)
        self.model_selector.currentTextChanged.connect(self.on_model_changed)
        toolbar.addWidget(self.model_selector)

        toolbar.addSeparator()

        self.gpu_checkbox = QCheckBox("🚀 Use GPU")
        self.gpu_checkbox.setChecked(self.use_gpu)
        self.gpu_checkbox.toggled.connect(self.on_gpu_toggled)
        toolbar.addWidget(self.gpu_checkbox)

        # Real-time audio toggle
        self.realtime_audio_checkbox = QCheckBox("🔊 Real-time Audio")
        self.realtime_audio_checkbox.setChecked(self.use_realtime_audio)
        self.realtime_audio_checkbox.toggled.connect(self.on_realtime_audio_toggled)
        toolbar.addWidget(self.realtime_audio_checkbox)

        btn_about = QAction("ℹ️ About", self)
        btn_about.triggered.connect(self.show_about_dialog)
        toolbar.addAction(btn_about)

        main_vlayout.addWidget(toolbar)
        print("    Toolbar setup complete...", flush=True)

        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create left side panel with Library and Queue
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)

        # Create splitter for Library and Queue
        library_queue_splitter = QSplitter(Qt.Orientation.Vertical)

        # Enhanced Library Panel with Selection Features
        library_panel = QWidget()
        library_layout = QVBoxLayout(library_panel)

        # Library header with controls
        library_header_layout = QHBoxLayout()
        library_header = QLabel("🎵 Library")
        library_header_layout.addWidget(library_header)
        library_header_layout.addStretch()

        # Selection controls with icon buttons
        self.select_all_btn = self.create_icon_button("☑", (32, 24), "Select All")
        self.select_all_btn.clicked.connect(self.select_all_songs)

        self.clear_selection_btn = self.create_icon_button(
            "☐", (32, 24), "Clear Selection"
        )
        self.clear_selection_btn.clicked.connect(self.clear_song_selection)

        self.batch_delete_btn = self.create_icon_button(
            "🗑", (32, 24), "Delete Selected"
        )
        self.batch_delete_btn.clicked.connect(self.batch_delete_songs)
        self.batch_delete_btn.setEnabled(False)

        self.add_to_queue_button = self.create_icon_button(
            "➕", (32, 24), "Add selected song to queue"
        )
        self.add_to_queue_button.clicked.connect(self.add_selected_to_queue)
        self.add_to_queue_button.setEnabled(False)  # Enabled when item selected

        library_header_layout.addWidget(self.select_all_btn)
        library_header_layout.addWidget(self.clear_selection_btn)
        library_header_layout.addWidget(self.batch_delete_btn)
        library_header_layout.addWidget(self.add_to_queue_button)

        library_layout.addLayout(library_header_layout)

        # Add search/filter box
        search_layout = QHBoxLayout()
        search_label = QLabel("🔍 Search:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Filter songs...")
        self.search_input.textChanged.connect(self.filter_library)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        library_layout.addLayout(search_layout)

        # Enhanced library list with drag & drop capability
        try:
            self.library_list = LibraryListWidget()
            print("LibraryListWidget created successfully", flush=True)
        except Exception as e:
            print(f"Failed to create LibraryListWidget: {e}", flush=True)
            # Fallback to standard QListWidget
            self.library_list = QListWidget()
            print("Using standard QListWidget as fallback", flush=True)

        self.library_list.itemClicked.connect(self.on_library_item_clicked)
        # Double-click can also be used for quick play (single click with Ctrl handles selection)
        self.library_list.itemDoubleClicked.connect(self.on_library_item_double_clicked)
        self.library_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.library_list.customContextMenuRequested.connect(
            self.show_library_context_menu
        )
        library_layout.addWidget(self.library_list)
        print("Library list added to layout", flush=True)

        # Library status
        self.library_status_label = QLabel("Library: 0 songs")
        library_layout.addWidget(self.library_status_label)

        # Add Library panel to splitter
        library_queue_splitter.addWidget(library_panel)

        # Create Queue Panel
        queue_panel = QWidget()
        queue_layout = QVBoxLayout(queue_panel)

        # Queue header with controls
        queue_header_layout = QHBoxLayout()
        queue_header = QLabel("🎤 Queue")
        queue_header_layout.addWidget(queue_header)
        queue_header_layout.addStretch()

        # Queue controls
        self.clear_queue_btn = self.create_icon_button("🗑", (32, 24), "Clear Queue")
        self.clear_queue_btn.clicked.connect(self.clear_queue)

        self.shuffle_queue_btn = self.create_icon_button(
            "🔀", (32, 24), "Shuffle Queue"
        )
        self.shuffle_queue_btn.clicked.connect(self.shuffle_queue)

        queue_header_layout.addWidget(self.clear_queue_btn)
        queue_header_layout.addWidget(self.shuffle_queue_btn)

        queue_layout.addLayout(queue_header_layout)

        # Queue list with drag & drop capability
        self.queue_list = QueueListWidget(self)
        self.queue_list.itemClicked.connect(self.on_queue_item_clicked)
        self.queue_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.queue_list.customContextMenuRequested.connect(self.show_queue_context_menu)
        queue_layout.addWidget(self.queue_list)

        # Queue status
        self.queue_status_label = QLabel("Queue: 0 songs")
        self.queue_status_label.setStyleSheet(
            "font-size: 10px; color: #888888; padding: 3px;"
        )
        queue_layout.addWidget(self.queue_status_label)

        # Add Queue panel to splitter
        library_queue_splitter.addWidget(queue_panel)

        # Set splitter proportions (Library slightly larger than Queue)
        library_queue_splitter.setSizes([300, 200])

        # Add the combined panel to the left layout
        left_layout.addWidget(library_queue_splitter)

        # Main content area with tabs
        main_area_widget = QWidget()
        main_area_layout = QVBoxLayout(main_area_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()
        main_area_layout.addWidget(self.tab_widget)

        # Main tab (video + controls)
        main_tab = QWidget()
        main_layout = QVBoxLayout(main_tab)
        self.tab_widget.addTab(main_tab, "Player")

        # Status tab
        status_tab = QWidget()
        status_layout = QVBoxLayout(status_tab)
        self.tab_widget.addTab(status_tab, "Status")

        # Enhanced Input Section
        input_group = QGroupBox("YouTube Download & Processing")
        input_layout = QVBoxLayout()

        url_layout = QHBoxLayout()
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter YouTube URL (single video only)...")
        self.url_input.returnPressed.connect(self.start_processing)
        self.process_button = QPushButton("🎬 Process New Song")
        self.process_button.clicked.connect(self.start_processing)

        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.clicked.connect(self.cancel_processing)
        self.cancel_button.setVisible(False)

        url_layout.addWidget(QLabel("URL:"))
        url_layout.addWidget(self.url_input)
        url_layout.addWidget(self.process_button)
        url_layout.addWidget(self.cancel_button)

        # Theme toggle button
        self.theme_button = self.create_icon_button(
            "🌙", (40, 32), "Toggle Theme (Light/Dark)"
        )
        self.theme_button.clicked.connect(self.toggle_theme)
        url_layout.addWidget(self.theme_button)

        input_layout.addLayout(url_layout)

        # Progress bar for processing
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        input_layout.addWidget(self.progress_bar)

        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # Video and Info Section (Horizontal Layout)
        video_info_layout = QHBoxLayout()

        # Responsive Video Display with Overlay Controls
        video_group = QGroupBox("Video Preview")
        video_group_layout = QVBoxLayout()

        # Create video container for overlay
        self.video_container = QWidget()
        self.video_container.setMinimumSize(480, 270)
        self.video_container.setSizePolicy(
            QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding
        )

        # Video widget fills the container
        print("    Creating QVideoWidget...", flush=True)
        self.video_widget = QVideoWidget(self.video_container)
        print("    QVideoWidget created...", flush=True)

        # Create overlay controls
        print("    Creating overlay controls...", flush=True)
        self.create_video_overlay_controls()
        print("    Overlay controls created...", flush=True)

        print("    Adding video container to layout...", flush=True)
        video_group_layout.addWidget(self.video_container)
        video_group.setLayout(video_group_layout)
        video_info_layout.addWidget(video_group)
        print("    Video container added to layout...", flush=True)

        # Compact Song Information Display
        print("    Creating song info display...", flush=True)
        info_group = QGroupBox("🎵 Info")
        print("    Info group box created...", flush=True)
        info_layout = QVBoxLayout()
        print("    Info layout created...", flush=True)

        self.title_label = QLabel("No Song Selected")
        print("    Title label created...", flush=True)
        self.title_label.setWordWrap(True)
        print("    Title label styled...", flush=True)

        info_compact_layout = QHBoxLayout()
        print("    Info compact layout created...", flush=True)
        self.key_label = QLabel("Key: -")
        self.bpm_label = QLabel("BPM: -")
        self.duration_label = QLabel("⏱ 00:00")
        print("    Info labels created...", flush=True)

        self.key_label.setStyleSheet("font-size: 10px;")
        self.bpm_label.setStyleSheet("font-size: 10px;")
        self.duration_label.setStyleSheet("font-size: 10px;")
        print("    Info labels styled...", flush=True)

        info_compact_layout.addWidget(self.key_label)
        info_compact_layout.addWidget(self.bpm_label)
        info_compact_layout.addWidget(self.duration_label)
        print("    Info labels added to compact layout...", flush=True)

        info_layout.addWidget(self.title_label)
        info_layout.addLayout(info_compact_layout)
        info_layout.addStretch()
        print("    Info widgets added to main info layout...", flush=True)

        info_group.setLayout(info_layout)
        info_group.setMaximumWidth(180)
        info_group.setMaximumHeight(120)
        video_info_layout.addWidget(info_group)
        print("    Info group configured and added to video layout...", flush=True)

        main_layout.addLayout(video_info_layout)
        print("    Song info display created and added...", flush=True)

        # Ultra-Compact Controls Section
        print("    Creating controls section...", flush=True)
        self.controls_group = QGroupBox("🎚️ Controls")
        controls_layout = QGridLayout()
        self.controls_group.setMaximumHeight(140)  # Allow more space for larger sliders

        # Compact sliders with better visibility
        slider_height = 24

        # Vocal Volume
        controls_layout.addWidget(QLabel("🎤 Vol:"), 0, 0)
        self.vocal_volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.vocal_volume_slider.setRange(0, 200)
        self.vocal_volume_slider.setValue(100)
        self.vocal_volume_slider.setFixedHeight(slider_height)
        self.vocal_volume_label = QLabel("100%")
        self.vocal_volume_label.setMinimumWidth(40)
        self.vocal_volume_slider.valueChanged.connect(self.on_slider_changed)
        controls_layout.addWidget(self.vocal_volume_slider, 0, 1)
        controls_layout.addWidget(self.vocal_volume_label, 0, 2)

        # Instrumental Volume
        controls_layout.addWidget(QLabel("🎵 Vol:"), 0, 3)
        self.instrumental_volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.instrumental_volume_slider.setRange(0, 200)
        self.instrumental_volume_slider.setValue(100)
        self.instrumental_volume_slider.setFixedHeight(slider_height)
        self.instrumental_volume_label = QLabel("100%")
        self.instrumental_volume_label.setMinimumWidth(40)
        self.instrumental_volume_slider.valueChanged.connect(self.on_slider_changed)
        controls_layout.addWidget(self.instrumental_volume_slider, 0, 4)
        controls_layout.addWidget(self.instrumental_volume_label, 0, 5)

        # Vocal Pitch
        controls_layout.addWidget(QLabel("🎤 Pitch:"), 1, 0)
        self.vocal_pitch_slider = QSlider(Qt.Orientation.Horizontal)
        self.vocal_pitch_slider.setRange(-12, 12)
        self.vocal_pitch_slider.setValue(0)
        self.vocal_pitch_slider.setFixedHeight(slider_height)
        self.vocal_pitch_slider.setToolTip(
            "Vocal pitch shift in semitones. Real-time mode: faster but may affect performance for extreme values."
        )
        self.vocal_pitch_label = QLabel("0 ♪")
        self.vocal_pitch_label.setMinimumWidth(40)
        self.vocal_pitch_slider.valueChanged.connect(self.on_slider_changed)
        controls_layout.addWidget(self.vocal_pitch_slider, 1, 1)
        controls_layout.addWidget(self.vocal_pitch_label, 1, 2)

        # Instrumental Pitch
        controls_layout.addWidget(QLabel("🎵 Pitch:"), 1, 3)
        self.instrumental_pitch_slider = QSlider(Qt.Orientation.Horizontal)
        self.instrumental_pitch_slider.setRange(-12, 12)
        self.instrumental_pitch_slider.setValue(0)
        self.instrumental_pitch_slider.setFixedHeight(slider_height)
        self.instrumental_pitch_slider.setToolTip(
            "Instrumental pitch shift in semitones. Real-time mode: faster but may affect performance for extreme values."
        )
        self.instrumental_pitch_label = QLabel("0 ♪")
        self.instrumental_pitch_label.setMinimumWidth(40)
        self.instrumental_pitch_slider.valueChanged.connect(self.on_slider_changed)
        controls_layout.addWidget(self.instrumental_pitch_slider, 1, 4)
        controls_layout.addWidget(self.instrumental_pitch_label, 1, 5)

        # Crossfade Control (compact)
        controls_layout.addWidget(QLabel("🔀 Fade:"), 2, 0)
        self.crossfade_slider = QSlider(Qt.Orientation.Horizontal)
        self.crossfade_slider.setRange(0, 10)
        self.crossfade_slider.setValue(int(self.fade_duration))
        self.crossfade_slider.setFixedHeight(slider_height)
        self.crossfade_label = QLabel(f"{int(self.fade_duration)}s")
        self.crossfade_label.setMinimumWidth(40)
        self.crossfade_slider.valueChanged.connect(self.on_crossfade_duration_changed)
        controls_layout.addWidget(self.crossfade_slider, 2, 1)
        controls_layout.addWidget(self.crossfade_label, 2, 2)

        # Reset Effects Button
        self.reset_effects_button = self.create_icon_button(
            "↺", (50, 32), "Reset all effects to defaults"
        )
        self.reset_effects_button.clicked.connect(self.reset_effects)
        controls_layout.addWidget(self.reset_effects_button, 2, 3)

        # Apply Mix Button (for non-realtime mode)
        self.apply_mix_button = QPushButton("APPLY MIX")
        self.apply_mix_button.setToolTip("Apply volume/pitch changes to the video file")
        self.apply_mix_button.clicked.connect(self.apply_effects_and_mix_auto)
        self.apply_mix_button.setMaximumWidth(100)
        controls_layout.addWidget(
            self.apply_mix_button, 2, 4, 1, 2
        )  # Span across two columns

        # Set minimum column widths to ensure sliders are visible
        controls_layout.setColumnMinimumWidth(1, 120)  # Slider columns
        controls_layout.setColumnMinimumWidth(4, 120)  # Slider columns
        controls_layout.setSpacing(8)  # Add spacing between elements

        self.controls_group.setLayout(controls_layout)
        main_layout.addWidget(self.controls_group)

        # Ultra-Compact Playback Controls
        self.playback_group = QGroupBox("⏯️ Player")
        playback_layout = QVBoxLayout()
        # Height will be managed by responsive design

        # Button row (compact)
        button_layout = QHBoxLayout()
        # Use icon-based buttons for better visual appeal
        self.play_button = self.create_icon_button("▶", (40, 32), "Play/Pause (Space)")
        self.pause_button = self.create_icon_button("⏸", (40, 32), "Pause")
        self.stop_button = self.create_icon_button("⏹", (40, 32), "Stop")

        print("    Connecting playback button signals...", flush=True)
        try:
            self.play_button.clicked.connect(self.toggle_play_pause)
            print("    Play button connected...", flush=True)
            self.pause_button.clicked.connect(self.pause_audio)
            print("    Pause button connected...", flush=True)
            self.stop_button.clicked.connect(self.stop_audio)
            print("    Stop button connected...", flush=True)
        except Exception as e:
            print(f"    Error connecting playback buttons: {e}", flush=True)

        self.play_button.setEnabled(False)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

        button_layout.addWidget(self.play_button)
        button_layout.addWidget(self.pause_button)
        button_layout.addWidget(self.stop_button)

        # Position slider and label (same row)
        print("    Creating track position slider...", flush=True)
        self.track_position_slider = QSlider(Qt.Orientation.Horizontal)
        self.track_position_slider.setRange(0, 1000)
        self.track_position_slider.setValue(0)
        self.track_position_slider.setFixedHeight(20)
        print("    Track position slider created...", flush=True)

        self.track_position_slider.sliderPressed.connect(self.on_track_slider_pressed)
        self.track_position_slider.sliderReleased.connect(self.on_track_slider_released)
        self.track_position_slider.sliderMoved.connect(self.on_track_slider_moved)

        self.playback_position_label = QLabel("00:00 / 00:00")
        self.playback_position_label.setMinimumWidth(80)
        print("    Playback position label created...", flush=True)

        button_layout.addWidget(self.track_position_slider)
        button_layout.addWidget(self.playback_position_label)
        print("    Track position widgets added to layout...", flush=True)

        # Don't add playbook group to main layout - controls are now in video overlay
        print("    Playback controls setup complete...", flush=True)

        # Move log section to status tab
        print("    Creating log section...", flush=True)
        self.log_group = QGroupBox("Status & Logging")
        print("    Log group box created...", flush=True)
        log_layout = QVBoxLayout()
        print("    Log layout created...", flush=True)
        self.log_output = QTextEdit()
        print("    Log text edit created...", flush=True)
        self.log_output.setReadOnly(True)
        self.log_output.setFont(QFont("Consolas", 10))
        print("    Log text edit configured...", flush=True)
        self.log_output.setSizePolicy(
            QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding
        )
        log_layout.addWidget(self.log_output)
        self.log_group.setLayout(log_layout)
        print("    Log widgets added to layout...", flush=True)
        status_layout.addWidget(self.log_group)  # Add to status tab instead of main tab
        print("    Log section added to status layout...", flush=True)

        # Initialize real-time audio streamer now that log_output exists
        print("    Creating real-time audio streamer...", flush=True)
        try:
            self.realtime_streamer = RealTimeAudioStreamer(log_callback=self.log_output.append)
            print("    Real-time audio streamer created...", flush=True)
        except Exception as e:
            print(f"    Real-time audio streamer initialization failed: {e}", flush=True)
            self.realtime_streamer = None

        print("    Setting up main splitter...", flush=True)
        # Add the left panel to the main splitter first
        self.main_splitter.addWidget(left_panel)
        # Then add the main area widget
        self.main_splitter.addWidget(main_area_widget)
        # Set proportional sizes instead of fixed pixels
        total_width = self.width() if self.width() > 0 else 1400
        library_width = max(250, min(400, int(total_width * 0.22)))  # 22% of width
        main_width = total_width - library_width
        self.main_splitter.setSizes([library_width, main_width])
        print("    Main splitter configured...", flush=True)

        print("    Adding main splitter to vertical layout...", flush=True)
        main_vlayout.addWidget(self.main_splitter)
        print("    Setting main layout on widget...", flush=True)
        self.setLayout(main_vlayout)
        print("    Main layout set on widget...", flush=True)

        # Set up media player with crossfade capability
        print("    Creating CrossfadePlayer...", flush=True)
        self.media_player = CrossfadePlayer(self, fade_duration=self.fade_duration)
        print("    CrossfadePlayer created, setting video output...", flush=True)
        self.media_player.setVideoOutput(self.video_widget)
        print("    Connecting media player signals...", flush=True)
        self.media_player.positionChanged.connect(self.on_media_position_changed)
        self.media_player.durationChanged.connect(self.on_media_duration_changed)
        self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)
        self.media_player.crossfadeFinished.connect(self.on_crossfade_finished)
        print("    Media player setup complete...", flush=True)

        self.shortcut_play = QShortcut(QKeySequence("Space"), self)
        self.shortcut_play.activated.connect(self.toggle_play_pause)

        # State for track slider interaction
        self.track_slider_pressed = False
        self.track_slider_moved_value = 0

        # Initialize theme after UI is built
        print("    Initializing theme...", flush=True)
        self.apply_current_theme()

        # Set initial button icons
        self.update_theme_button_icon()
        self.update_play_button_icon(False)

        # Initialize Apply Mix button visibility based on real-time audio setting
        if hasattr(self, "apply_mix_button"):
            self.apply_mix_button.setVisible(not self.use_realtime_audio)

        # Trigger initial responsive layout
        print("    Setting up initial resize...", flush=True)
        QTimer.singleShot(100, self.trigger_initial_resize)
        print("    initUI() complete!", flush=True)

    def create_video_overlay_controls(self):
        """Create overlay controls on top of the video widget."""
        print("      Creating overlay widget...", flush=True)
        # Create overlay widget directly on video container for maximum visibility
        self.overlay_widget = QWidget(self.video_container)
        print("      Overlay widget created on video container...", flush=True)
        print("      Setting overlay properties...", flush=True)

        # Make overlay completely opaque and always visible
        self.overlay_widget.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, False)
        self.overlay_widget.setWindowFlags(Qt.WindowType.Widget)
        # Make background more visible with higher opacity
        self.overlay_widget.setStyleSheet("background: rgba(0, 0, 0, 200); border: 2px solid rgba(100, 100, 100, 255);")
        self.overlay_widget.raise_()  # Bring to front
        self.overlay_widget.show()
        self.overlay_widget.setVisible(True)
        # Force the overlay to stay on top
        self.overlay_widget.setWindowFlags(Qt.WindowType.Widget | Qt.WindowType.WindowStaysOnTopHint)
        print("      Overlay properties set with enhanced visibility...", flush=True)

        # Create overlay layout
        print("      Creating overlay layout...", flush=True)
        overlay_layout = QVBoxLayout(self.overlay_widget)
        overlay_layout.addStretch()  # Push controls to bottom
        print("      Overlay layout created...", flush=True)

        # Control panel with bright, opaque background for maximum visibility
        print("      Creating control panel...", flush=True)
        self.overlay_control_panel = QWidget()
        print("      Control panel widget created...", flush=True)
        print("      Setting control panel style...", flush=True)
        self.overlay_control_panel.setStyleSheet(
            """
            QWidget {
                background: rgba(40, 40, 40, 250);
                border-radius: 10px;
                border: 3px solid rgba(150, 150, 150, 255);
            }
        """
        )
        self.overlay_control_panel.setFixedHeight(90)  # Increased height for better visibility
        # Ensure the control panel is always visible
        self.overlay_control_panel.show()
        self.overlay_control_panel.raise_()
        print("      Control panel styled and made visible...", flush=True)

        # Control layout
        print("      Creating control layout...", flush=True)
        control_layout = QHBoxLayout(self.overlay_control_panel)
        control_layout.setContentsMargins(15, 10, 15, 10)  # More padding
        print("      Control layout created...", flush=True)

        # Play/Pause/Stop buttons with icons
        print("      Creating overlay buttons...", flush=True)
        self.overlay_previous_button = self.create_icon_button("⏮", (50, 40), "Previous Track")
        self.overlay_play_button = self.create_icon_button("▶", (50, 40), "Play")  # Even bigger buttons
        self.overlay_pause_button = self.create_icon_button("⏸", (50, 40), "Pause")
        self.overlay_stop_button = self.create_icon_button("⏹", (50, 40), "Stop")
        self.overlay_next_button = self.create_icon_button("⏭", (50, 40), "Next Track")
        print("      Overlay buttons created...", flush=True)

        # Style overlay buttons with maximum visibility
        print("      Creating button styles...", flush=True)
        overlay_button_style = """
            QPushButton {
                background: rgba(80, 80, 80, 255);
                color: white;
                border: 3px solid rgba(200, 200, 200, 255);
                border-radius: 8px;
                font-weight: bold;
                font-size: 18px;
                padding: 10px 15px;
                margin: 3px;
                min-width: 50px;
                min-height: 45px;
            }
            QPushButton:hover {
                background: rgba(120, 120, 120, 255);
                border: 3px solid rgba(255, 255, 255, 255);
                color: white;
            }
            QPushButton:pressed {
                background: rgba(180, 180, 180, 255);
                color: black;
                border: 3px solid rgba(255, 255, 255, 255);
            }
            QPushButton:disabled {
                background: rgba(60, 60, 60, 200);
                color: gray;
                border: 3px solid rgba(100, 100, 100, 200);
            }
        """
        print("      Button styles created...", flush=True)

        print("      Applying button styles...", flush=True)
        self.overlay_previous_button.setStyleSheet(overlay_button_style)
        self.overlay_play_button.setStyleSheet(overlay_button_style)
        self.overlay_pause_button.setStyleSheet(overlay_button_style)
        self.overlay_stop_button.setStyleSheet(overlay_button_style)
        self.overlay_next_button.setStyleSheet(overlay_button_style)

        # Position slider
        print("      Creating position slider...", flush=True)
        self.overlay_position_slider = QSlider(Qt.Orientation.Horizontal)
        self.overlay_position_slider.setRange(0, 1000)
        self.overlay_position_slider.setValue(0)
        self.overlay_position_slider.setMinimumHeight(25)  # Make slider even taller
        print("      Position slider created...", flush=True)

        print("      Styling position slider...", flush=True)
        self.overlay_position_slider.setStyleSheet(
            """
            QSlider::groove:horizontal {
                background: rgba(100, 100, 100, 255);
                height: 12px;
                border-radius: 6px;
                border: 2px solid rgba(150, 150, 150, 255);
            }
            QSlider::handle:horizontal {
                background: white;
                border: 3px solid rgba(220, 220, 220, 255);
                width: 22px;
                height: 22px;
                border-radius: 11px;
                margin: -5px 0;
            }
            QSlider::handle:horizontal:hover {
                background: rgba(240, 240, 240, 255);
                border: 3px solid rgba(255, 255, 255, 255);
            }
            QSlider::handle:horizontal:pressed {
                background: rgba(200, 200, 200, 255);
                border: 3px solid rgba(255, 255, 255, 255);
            }
        """
        )
        print("      Position slider styled...", flush=True)

        # Connect slider events (same as main slider)
        self.overlay_position_slider.sliderPressed.connect(self.on_track_slider_pressed)
        self.overlay_position_slider.sliderReleased.connect(self.on_track_slider_released)
        self.overlay_position_slider.sliderMoved.connect(self.on_track_slider_moved)

        # Connect overlay buttons to main playback functions
        self.overlay_previous_button.clicked.connect(self.play_previous_track)
        self.overlay_play_button.clicked.connect(self.toggle_play_pause)
        self.overlay_pause_button.clicked.connect(self.pause_audio)
        self.overlay_stop_button.clicked.connect(self.stop_audio)
        self.overlay_next_button.clicked.connect(self.play_next_track)

        # Time label
        self.overlay_time_label = QLabel("00:00 / 00:00")
        self.overlay_time_label.setStyleSheet(
            """
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 16px;
                background: rgba(60, 60, 60, 255);
                padding: 6px 12px;
                border-radius: 6px;
                border: 2px solid rgba(120, 120, 120, 255);
                min-width: 80px;
            }
        """
        )

        # Add widgets to control layout
        control_layout.addWidget(self.overlay_previous_button)
        control_layout.addWidget(self.overlay_play_button)
        control_layout.addWidget(self.overlay_pause_button)
        control_layout.addWidget(self.overlay_stop_button)
        control_layout.addWidget(self.overlay_next_button)
        control_layout.addStretch(1)  # Add some space
        control_layout.addWidget(self.overlay_position_slider, 3)  # Give slider more space
        control_layout.addStretch(1)  # Add some space
        control_layout.addWidget(self.overlay_time_label)

        # Add control panel to overlay
        overlay_layout.addWidget(self.overlay_control_panel)
        overlay_layout.setContentsMargins(20, 20, 20, 20)  # More margins

        # Make overlay initially visible and ensure it stays visible
        self.overlay_widget.show()
        self.overlay_widget.raise_()  # Ensure it's on top
        print("      Overlay widget made visible and raised...", flush=True)

        # Create hide timer for overlay (disabled for now to keep controls visible)
        self.overlay_hide_timer = QTimer(self)
        self.overlay_hide_timer.setSingleShot(True)
        # Don't connect the timeout to hide - keep controls visible
        # self.overlay_hide_timer.timeout.connect(self.overlay_widget.hide)

        # Connect hover events via an event filter
        self.video_container.setMouseTracking(True)
        self.video_container.installEventFilter(self)

        # Ensure overlay resizes with container
        self.video_container.resizeEvent = self.resize_overlay
        print("      Overlay controls setup complete...", flush=True)

    def show_overlay(self, event):
        """Show overlay controls when mouse enters video area."""
        self.overlay_widget.show()

    def hide_overlay(self, event):
        """Hide overlay controls when mouse leaves video area."""
        self.overlay_widget.hide()

    def resize_overlay(self, event):
        """Resize overlay to match video container."""
        if hasattr(self, "overlay_widget"):
            print(f"Resizing overlay from resize_overlay to: {self.video_container.size()}", flush=True)
            self.overlay_widget.resize(self.video_container.size())
            self.overlay_widget.move(0, 0)  # Ensure it's positioned at the top-left of container
            self.overlay_widget.raise_()  # Bring to front
            self.overlay_widget.show()  # Ensure it stays visible
            print("Overlay resized, moved, raised, and made visible", flush=True)
            self.video_widget.resize(self.video_container.size())

    def trigger_initial_resize(self):
        """Force an initial resize event to apply responsive layout."""
        self.resize(self.size())
        
        # Ensure overlay is properly sized to video container
        if hasattr(self, "overlay_widget") and hasattr(self, "video_container"):
            print(f"Resizing overlay to video container size: {self.video_container.size()}", flush=True)
            self.overlay_widget.resize(self.video_container.size())
            self.overlay_widget.move(0, 0)  # Position at top-left of container
            self.overlay_widget.raise_()  # Bring to front
            # Make sure overlay is visible
            self.overlay_widget.show()
            print("Overlay should now be visible and properly sized", flush=True)
            
            # Also make sure the control panel is visible
            if hasattr(self, "overlay_control_panel"):
                self.overlay_control_panel.show()
                print("Overlay control panel made visible", flush=True)

    def closeEvent(self, event):
        """Handle window close event."""
        # Stop any running threads
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.cancel()
            self.processing_thread.wait()
        if self.mixing_thread and self.mixing_thread.isRunning():
            self.mixing_thread.wait()

        # Stop audio playback
        if self.use_realtime_audio:
            self.realtime_streamer.stop_playback()
        else:
            self.media_player.stop()

        # Save preferences
        save_preferences(self.preferences)

        # Clean up temporary files
        try:
            temp_dir = tempfile.gettempdir()
            for item in os.listdir(temp_dir):
                if item.startswith("tmp") and item.endswith(".wav"):
                    os.remove(os.path.join(temp_dir, item))
        except Exception as e:
            print(f"Error cleaning temp files: {e}")

        event.accept()

    # --- New UI Event Handlers ---
    def on_model_changed(self, model_name):
        """Handle AI model selection change."""
        self.separation_model = model_name
        self.preferences["separation_model"] = model_name
        save_preferences(self.preferences)
        self.log_output.append(f"Switched to AI model: {self.separation_model}")

    def toggle_theme(self):
        """Toggle between light and dark themes."""
        if self.current_theme == "dark":
            self.current_theme = "light"
            # Update icon to show moon (for switching back to dark)
            self.update_theme_button_icon()
        else:
            self.current_theme = "dark"
            # Update icon to show sun (for switching back to light)
            self.update_theme_button_icon()

        self.preferences["theme"] = self.current_theme
        save_preferences(self.preferences)

        self.apply_current_theme()

    def update_theme_button_icon(self):
        """Update the theme button icon based on current theme."""
        if self.current_theme == "dark":
            # Show sun icon to indicate switching to light
            icon_text = "☀"
            tooltip = "Switch to Light Theme"
        else:
            # Show moon icon to indicate switching to dark
            icon_text = "🌙"
            tooltip = "Switch to Dark Theme"

        app = QApplication.instance()
        theme_colors = app.property("theme_colors")
        size = (40, 32)
        pixmap = self._create_pixmap_from_text(icon_text, size, theme_colors)

        self.theme_button.setIcon(QIcon(pixmap))
        self.theme_button.setToolTip(tooltip)

    def update_play_button_icon(self, is_playing=False):
        """Update the play button icon based on playing state."""
        if is_playing:
            icon_text = "⏸"
            tooltip = "Pause"
        else:
            icon_text = "▶"
            tooltip = "Play"

        app = QApplication.instance()
        theme_colors = app.property("theme_colors")
        size = (40, 32)
        pixmap = self._create_pixmap_from_text(icon_text, size, theme_colors)

        self.play_button.setIcon(QIcon(pixmap))
        self.play_button.setToolTip(tooltip)

    def on_gpu_toggled(self, checked):
        self.use_gpu = checked
        self.preferences["use_gpu"] = checked
        save_preferences(self.preferences)
        self.log_output.append(
            f"GPU acceleration {'enabled' if checked else 'disabled'}."
        )

    def on_realtime_audio_toggled(self, checked):
        """Handle real-time audio toggle."""
        self.use_realtime_audio = checked
        self.preferences["use_realtime_audio"] = checked
        save_preferences(self.preferences)

        # Show/hide the "Apply Mix" button based on the mode
        self.apply_mix_button.setVisible(not checked)

        if checked:
            self.log_output.append(
                "🔊 Real-time audio enabled. Effects will be applied live."
            )
            # If a song is loaded, switch to real-time playback
            if self.current_unique_id:
                self.system_initiated_stop = True  # Set flag before action
                self.load_song_for_playback(
                    self.current_unique_id, play_after_load=False
                )
                self.system_initiated_stop = False  # Reset flag after
        else:
            self.log_output.append(
                "💿 Real-time audio disabled. Changes require 'Apply Mix'."
            )
            # If a song is loaded, switch to pre-mixed playback
            if self.current_unique_id:
                self.load_song_for_playback(
                    self.current_unique_id, play_after_load=False
                )

    def show_about_dialog(self):
        """Show an about dialog."""
        QMessageBox.about(
            self,
            "About Karaoke Studio",
            "<h2>🎤 Advanced Karaoke Studio - UVR Edition</h2>"
            "<p>Version 1.2.0</p>"
            "<p>This application allows you to download YouTube videos, "
            "separate vocals from instrumentals using state-of-the-art AI models, "
            "and practice karaoke with real-time audio adjustments.</p>"
            "<p><b>Features:</b></p>"
            "<ul>"
            "<li>YouTube video download and audio extraction.</li>"
            "<li>High-quality vocal separation with UVR models.</li>"
            "<li>Real-time pitch and volume controls.</li>"
            "<li>Karaoke video generation.</li>"
            "<li>Song library and queue management.</li>"
            "<li>Light and Dark themes.</li>"
            "</ul>"
            "<p>Developed with PyQt6 and Python.</p>",
        )

    def apply_current_theme(self):
        """Apply the current theme to the application."""
        app = QApplication.instance()
        apply_theme(app, self.current_theme)

        # Refresh all icons after theme change
        QTimer.singleShot(100, self.refresh_all_button_icons)

        # Update theme-specific styles for current theme
        self.update_styles_for_theme()

    def update_styles_for_theme(self):
        """Update specific widget styles that are not easily handled by a global stylesheet."""
        theme_colors = QApplication.instance().property("theme_colors")
        if not theme_colors:
            return

        # Update any labels with hardcoded colors (with safety checks)
        if hasattr(self, "library_status_label") and self.library_status_label:
            try:
                self.library_status_label.setStyleSheet(
                    f"color: {theme_colors['text_secondary']}; font-size: 10px;"
                )
            except RuntimeError:
                pass  # Widget has been deleted

        if hasattr(self, "queue_status_label") and self.queue_status_label:
            try:
                self.queue_status_label.setStyleSheet(
                    f"font-size: 10px; color: {theme_colors['text_secondary']}; padding: 3px;"
                )
            except RuntimeError:
                pass  # Widget has been deleted

        # Update overlay controls (with safety checks)
        # Skip overlay styling updates to preserve our custom styles
        pass

        # Refresh list widget items to apply new selection colors (with safety checks)
        try:
            self.load_library()
        except Exception:
            pass  # Library widget might be deleted

        try:
            self.refresh_queue_display()
        except Exception:
            pass  # Queue widget might be deleted

    def resizeEvent(self, event):
        """Properly override the resizeEvent method."""
        super().resizeEvent(event)
        self.handle_resize(event)  # Call the existing handler logic

    def eventFilter(self, source, event):
        """Filter events to show/hide video overlay."""
        if source == self.video_container:
            if event.type() == event.Type.Enter:
                if hasattr(self, "overlay_widget"):
                    self.overlay_widget.show()
                return True
            elif event.type() == event.Type.Leave:
                # For debugging, keep overlay visible
                # if hasattr(self, "overlay_widget"):
                #     self.overlay_widget.hide()
                return True
        return super().eventFilter(source, event)

    def handle_resize(self, event):
        """Handle window resizing for responsive UI."""
        # The best practice is to rely on Qt's layout managers.
        # Let the layouts configured in initUI handle responsive behavior automatically.
        self.update_styles_for_theme()

        # Make video overlay responsive
        if hasattr(self, "overlay_widget") and hasattr(self, "video_container"):
            print(f"Resizing overlay in handle_resize to: {self.video_container.size()}", flush=True)
            self.overlay_widget.resize(self.video_container.size())
            # Ensure overlay stays visible
            self.overlay_widget.show()

        # Adjust splitter sizes based on window width
        total_width = self.width()
        library_width = max(250, min(450, int(total_width * 0.25)))
        main_width = total_width - library_width
        self.main_splitter.setSizes([library_width, main_width])

    # --- Processing ---
    def start_processing(self):
        youtube_url = self.url_input.text().strip()
        if not youtube_url:
            self.log_output.append("Please enter a YouTube URL.")
            return

        print("=== STARTING PROCESSING ===", flush=True)
        self.log_output.clear()
        self.log_output.append("🎬 Starting YouTube video processing...")
        self.log_output.append(f"📍 URL: {youtube_url}")
        self.log_output.append(f"🤖 AI Model: {self.separation_model}")
        self.log_output.append(f"🚀 GPU: {'Enabled' if self.use_gpu else 'Disabled'}")
        
        self.process_button.setEnabled(False)
        self.cancel_button.setVisible(True)
        self.url_input.setEnabled(False)

        # Create and show progress dialog (optional - can be disabled for debugging)
        # Set this to False to disable the modal dialog and only use status logging
        use_progress_dialog = False  # Changed to False to disable dialog and see status better
        
        if use_progress_dialog:
            try:
                # Clean up any existing dialog first
                if hasattr(self, "progress_dialog") and self.progress_dialog:
                    print("Cleaning up existing progress dialog", flush=True)
                    self.progress_dialog.close()
                    self.progress_dialog = None

                print("Creating new progress dialog", flush=True)
                self.progress_dialog = QProgressDialog(
                    "Downloading and processing video...", "Cancel", 0, 100, self
                )
                self.progress_dialog.setWindowTitle("Processing YouTube Video")
                self.progress_dialog.setModal(True)
                self.progress_dialog.setMinimumDuration(0)  # Show immediately
                self.progress_dialog.setValue(0)
                self.progress_dialog.canceled.connect(self.cancel_processing)
                self.progress_dialog.show()
                print("Progress dialog created and shown", flush=True)
            except Exception as e:
                print(f"ERROR creating progress dialog: {e}", flush=True)
                self.log_output.append(f"⚠️ Warning: Could not create progress dialog: {e}")
                self.progress_dialog = None
        else:
            print("Progress dialog disabled - using status logging only", flush=True)
            self.log_output.append("📊 Progress dialog disabled - watch this log for updates")
            self.progress_dialog = None

        # Always show progress bar in main UI
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            print("Progress bar shown", flush=True)
        except Exception as e:
            print(f"ERROR showing progress bar: {e}", flush=True)

        # Use the selected separation model
        print(f"Starting processing thread with model: {self.separation_model}", flush=True)
        try:
            self.processing_thread = ProcessingThread(
                youtube_url, self.separation_model, self.use_gpu
            )
            self.processing_thread.log_message.connect(self.update_log)
            self.processing_thread.progress_update.connect(self.update_progress)
            self.processing_thread.finished_processing.connect(self.on_processing_finished)
            # Don't set up automatic cleanup - we'll handle it manually
            self.processing_thread.start()
            print("Processing thread started successfully", flush=True)
        except Exception as e:
            print(f"ERROR starting processing thread: {e}", flush=True)
            self.log_output.append(f"❌ Error starting processing: {e}")
            self.reset_processing_ui()

    def cancel_processing(self):
        """Cancel the current processing operation."""
        print("=== CANCELLING PROCESSING ===", flush=True)
        self.log_output.append("❌ Cancelling processing...")
        
        if (
            hasattr(self, "processing_thread")
            and self.processing_thread
            and self.processing_thread.isRunning()
        ):
            print("Cancelling processing thread", flush=True)
            self.processing_thread.cancel()
            # Wait for thread to finish gracefully
            self.processing_thread.wait(3000)  # Wait up to 3 seconds
            print("Processing thread cancelled", flush=True)

        # Clean up progress dialog
        try:
            if hasattr(self, "progress_dialog") and self.progress_dialog:
                print("Closing progress dialog", flush=True)
                self.progress_dialog.close()
                self.progress_dialog = None
        except Exception as e:
            print(f"ERROR closing progress dialog: {e}", flush=True)
            self.progress_dialog = None

        self.reset_processing_ui()
        self.log_output.append("✅ Processing cancelled by user")

    def on_processing_finished(
        self,
        video_file,
        audio_file,
        vocals_file,
        instrumental_file,
        title,
        key,
        bpm,
        unique_id,
    ):
        print("=== PROCESSING FINISHED ===", flush=True)
        print(f"Video: {video_file}", flush=True)
        print(f"Audio: {audio_file}", flush=True)
        print(f"Vocals: {vocals_file}", flush=True)
        print(f"Instrumental: {instrumental_file}", flush=True)
        print(f"Title: {title}", flush=True)
        print(f"Key: {key}", flush=True)
        print(f"BPM: {bpm}", flush=True)
        print(f"Unique ID: {unique_id}", flush=True)
        
        # Clean up progress dialog first
        try:
            if hasattr(self, "progress_dialog") and self.progress_dialog:
                print("Closing progress dialog", flush=True)
                self.progress_dialog.close()
                self.progress_dialog = None
        except Exception as e:
            print(f"ERROR closing progress dialog: {e}", flush=True)
            self.progress_dialog = None

        self.reset_processing_ui()

        if video_file and vocals_file and instrumental_file:
            # Update metadata safely from the main thread
            self.metadata[unique_id] = {
                "title": title,
                "url": self.url_input.text(),
                "key": key,
                "bpm": bpm,
                "files": get_song_files(unique_id),
            }
            save_metadata(self.metadata)

            # Add to library list
            self.add_song_to_library_list(unique_id, title)
            self.log_output.append(
                f"✅ Successfully processed and added '{title}' to library."
            )
        else:
            # Even if processing failed, save any partial metadata we obtained
            if unique_id and title:
                partial_metadata = {
                    "title": title,
                    "url": self.url_input.text(),
                    "key": key if key else "Unknown",
                    "bpm": bpm if bpm else "Unknown",
                    "files": get_song_files(unique_id),
                    "processing_status": "partial_failure",
                }
                self.metadata[unique_id] = partial_metadata
                save_metadata(self.metadata)

                # Add to library list even with partial data
                self.add_song_to_library_list(unique_id, title)
                self.log_output.append(
                    f"⚠️ Processing partially failed for '{title}', but saved available metadata."
                )
            else:
                self.log_output.append("❌ Processing failed. Check logs for details.")

        # Clean up the thread
        if hasattr(self, "processing_thread") and self.processing_thread:
            self.processing_thread.deleteLater()
            self.processing_thread = None

    def reset_processing_ui(self):
        """Reset the UI elements after processing is complete or cancelled."""
        print("Resetting processing UI", flush=True)
        try:
            self.url_input.setEnabled(True)
            self.url_input.clear()
            self.process_button.setEnabled(True)
            self.cancel_button.setVisible(False)
            self.progress_bar.setVisible(False)
            self.progress_bar.setRange(0, 100)
            print("Processing UI reset successfully", flush=True)
        except Exception as e:
            print(f"ERROR resetting processing UI: {e}", flush=True)

    def load_library(self):
        """Load songs from metadata into the library list."""
        # Safety check to prevent accessing deleted widgets
        if not hasattr(self, "library_list") or not self.library_list:
            return

        try:
            self.library_list.clear()
        except RuntimeError:
            # Library list widget has been deleted
            return

        self.metadata = load_metadata()
        if not os.path.exists(LIBRARY_DIR):
            os.makedirs(LIBRARY_DIR, exist_ok=True)
            return

        items_to_add = []

        # Check if the directory is empty (except for mixed and remux directories)
        library_contents = [d for d in os.listdir(LIBRARY_DIR)
                           if d not in ["mixed", "remux", "uvr_models", "__pycache__"] and
                           os.path.isdir(os.path.join(LIBRARY_DIR, d))]
        
        if not library_contents:
            # Library is empty, just update status and return
            self.update_library_status()
            try:
                self.log_output.append("Library loaded. Found 0 songs.")
            except RuntimeError:
                pass
            return

        # Correctly parse the unique_id from the folder name format (Title_id12345678)
        for dir_name in library_contents:
            item_path = os.path.join(LIBRARY_DIR, dir_name)
            if (
                os.path.isdir(item_path)
                and dir_name not in ["uvr_models", "__pycache__"]
                and "_" in dir_name
                and len(dir_name.rsplit("_", 1)[-1]) == 8  # More robust check
            ):

                # Use rsplit to safely separate the ID from the title
                sanitized_title, short_id = dir_name.rsplit("_", 1)
                full_unique_id = None

                # Find the full ID by matching the short ID in our metadata
                for uid_key in self.metadata.keys():
                    if uid_key.startswith(short_id):
                        full_unique_id = uid_key
                        break

                if not full_unique_id:
                    continue  # Skip if we can't find its metadata

                files = get_song_files(full_unique_id)

                if all(
                    os.path.exists(f)
                    for f in [files["video"], files["vocals"], files["instrumental"]]
                ):
                    title = self.metadata.get(full_unique_id, {}).get(
                        "title", f"Unknown ({full_unique_id})"
                    )
                    items_to_add.append(
                        (
                            title,
                            files["video"],
                            files["vocals"],
                            files["instrumental"],
                            full_unique_id,
                        )
                    )

        # Check for legacy files and migrate them
        self.migrate_legacy_files()

        # Sort items by title
        items_to_add.sort(key=lambda x: x[0].lower())
        for (
            title,
            video_path,
            vocals_path,
            instrumental_path,
            unique_id,
        ) in items_to_add:
            try:
                item = QListWidgetItem(title)
                item.setData(
                    Qt.ItemDataRole.UserRole,
                    (video_path, vocals_path, instrumental_path, unique_id),
                )

                # Set icon based on selection state
                is_selected = unique_id in self.selected_songs
                item.setIcon(self.get_song_icon(is_selected))

                self.library_list.addItem(item)
            except RuntimeError:
                # Library list widget has been deleted
                return

        self.update_library_status()
        try:
            self.log_output.append(
                f"Library loaded. Found {self.library_list.count()} songs."
            )
        except RuntimeError:
            # Either library_list or log_output has been deleted
            pass

    def update_library_status(self):
        """Updates the library status label."""
        try:
            count = self.library_list.count()
            self.library_status_label.setText(
                f"Library: {count} song{'s' if count != 1 else ''}"
            )
        except RuntimeError:
            # Widget has been deleted
            pass

    def add_song_to_library_list(self, unique_id, title):
        """Add a single song item to the library list widget."""
        # Fetch file paths and store the complete data tuple
        metadata_entry = self.metadata.get(unique_id, {})
        files = metadata_entry.get("files", {})

        if not all(key in files for key in ["video", "vocals", "instrumental"]):
            self.log_output.append(
                f"Error: Could not add '{title}' to library, file paths missing from metadata."
            )
            return

        video_path = files["video"]
        vocals_path = files["vocals"]
        instrumental_path = files["instrumental"]

        # Ensure required files actually exist on disk
        if not all(
            os.path.exists(p) for p in [video_path, vocals_path, instrumental_path]
        ):
            self.log_output.append(
                f"Error: Could not add '{title}' to library, one or more files are missing from disk."
            )
            return

        item = QListWidgetItem(title)
        # Store data in the correct tuple format, consistent with load_library
        item.setData(
            Qt.ItemDataRole.UserRole,
            (video_path, vocals_path, instrumental_path, unique_id),
        )

        key = metadata_entry.get("key", "N/A")
        bpm = metadata_entry.get("bpm", "N/A")
        item.setToolTip(f"ID: {unique_id}\nKey: {key}, BPM: {bpm}")

        self.library_list.addItem(item)
        self.update_library_status()

    def filter_library(self):
        """Filter the library list based on search text."""
        search_text = self.search_input.text().lower().strip()

        # Show all items if search is empty
        if not search_text:
            for i in range(self.library_list.count()):
                item = self.library_list.item(i)
                item.setHidden(False)
            return

        # Hide items that don't match the search
        visible_count = 0
        for i in range(self.library_list.count()):
            item = self.library_list.item(i)
            title = item.text().lower()

            # Check if search text is in the title
            matches = search_text in title
            item.setHidden(not matches)

            if matches:
                visible_count += 1

        # Update status in log
        if visible_count == 0:
            self.log_output.append(f"No songs found matching '{search_text}'")
        else:
            self.log_output.append(
                f"Found {visible_count} songs matching '{search_text}'"
            )

    def select_all_songs(self):
        """Select all songs in the library."""
        for i in range(self.library_list.count()):
            item = self.library_list.item(i)
            data = item.data(Qt.ItemDataRole.UserRole)
            if data:
                unique_id = data[3]
                self.selected_songs.add(unique_id)
                item.setIcon(self.get_song_icon(True))
        self.update_batch_delete_button()

    def clear_song_selection(self):
        """Clear all song selections."""
        self.selected_songs.clear()
        for i in range(self.library_list.count()):
            item = self.library_list.item(i)
            item.setIcon(self.get_song_icon(False))
        self.update_batch_delete_button()

    def toggle_song_selection(self, unique_id, item):
        """Toggle selection state of a song."""
        if unique_id in self.selected_songs:
            self.selected_songs.remove(unique_id)
            item.setIcon(self.get_song_icon(False))
        else:
            self.selected_songs.add(unique_id)
            item.setIcon(self.get_song_icon(True))
        self.update_batch_delete_button()

    def get_song_icon(self, selected=False):
        """Get the icon for a song based on the current theme and selection state."""
        theme_colors = QApplication.instance().property("theme_colors")
        if not theme_colors:
            return QIcon()  # Return empty icon if theme not set

        # Create embedded icon using QPixmap instead of file-based approach
        # This is more robust for packaged applications
        return self.create_embedded_music_icon(selected, theme_colors)

    def create_embedded_music_icon(self, selected=False, theme_colors=None):
        """Create a music note icon programmatically using QPainter."""
        if not theme_colors:
            theme_colors = QApplication.instance().property("theme_colors")
            if not theme_colors:
                return QIcon()

        # Create a 24x24 pixmap
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Choose color based on selection state
        color_str = theme_colors["text_accent"] if selected else theme_colors["text_primary"]
        color = QColor(color_str)

        # Set up pen for drawing
        pen = QPen(color, 2)
        painter.setPen(pen)
        painter.setBrush(color)

        # Draw a simple music note
        # Note head (filled circle)
        painter.drawEllipse(2, 14, 8, 6)

        # Note stem (vertical line)
        painter.drawLine(10, 17, 10, 4)

        # Note flag (curved line at top)
        painter.drawLine(10, 4, 18, 6)
        painter.drawLine(10, 6, 16, 8)

        painter.end()

        return QIcon(pixmap)

    def refresh_all_button_icons(self):
        """Refresh all icons in the UI to match the current theme."""
        # Update theme button icon
        try:
            self.update_theme_button_icon()
        except (RuntimeError, AttributeError):
            pass  # Theme button might be deleted

        # Refresh all icon buttons by recreating their icons
        icon_buttons = [
            ("select_all_btn", "☑", (32, 24), "Select All"),
            ("clear_selection_btn", "☐", (32, 24), "Clear Selection"),
            ("batch_delete_btn", "🗑", (32, 24), "Delete Selected"),
            ("clear_queue_btn", "🗑", (32, 24), "Clear Queue"),
            ("shuffle_queue_btn", "🔀", (32, 24), "Shuffle Queue"),
        ]

        for button_name, icon_text, size, tooltip in icon_buttons:
            try:
                if hasattr(self, button_name):
                    button = getattr(self, button_name)
                    self.refresh_icon_button(button, icon_text, size, tooltip)
            except (RuntimeError, AttributeError):
                pass  # Button might be deleted

        # Reload library and queue to update song icons
        try:
            self.load_library()
        except Exception:
            pass  # Library widget might be deleted

        try:
            self.refresh_queue_display()
        except Exception:
            pass  # Queue widget might be deleted

    def refresh_icon_button(self, button, icon_text, size, tooltip):
        """Refresh a single icon button with current theme colors."""
        if not button:
            return

        app = QApplication.instance()
        theme_colors = app.property("theme_colors")
        pixmap = self._create_pixmap_from_text(icon_text, size, theme_colors)

        button.setIcon(QIcon(pixmap))
        if tooltip:
            button.setToolTip(tooltip)

    # === Missing Methods - Basic Implementations ===

    def batch_delete_songs(self):
        selected_items = self.library_list.selectedItems()
        if not selected_items:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Permanently delete {len(selected_items)} selected song(s) and all associated files?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            for item in selected_items:
                data = item.data(Qt.ItemDataRole.UserRole)
                if isinstance(data, tuple) and len(data) > 3:
                    unique_id = data[3]  # Extract from tuple format
                else:
                    unique_id = data  # Fallback for simple format

                if unique_id == self.current_unique_id:
                    self.stop_audio()  # Stop if deleting current song
                try:
                    song_dir = get_song_directory(unique_id)
                    if os.path.exists(song_dir):
                        shutil.rmtree(song_dir)
                    if unique_id in self.metadata:
                        del self.metadata[unique_id]
                    self.log_output.append(f"Deleted song: {item.text()}")
                except Exception as e:
                    self.log_output.append(f"Error deleting {item.text()}: {e}")
            save_metadata(self.metadata)
            self.load_library()

    def update_selection_buttons(self):
        """Update the state of selection-related buttons."""
        has_selection = len(self.library_list.selectedItems()) > 0
        if hasattr(self, "add_to_queue_button"):
            self.add_to_queue_button.setEnabled(has_selection)
        if hasattr(self, "batch_delete_btn"):
            self.batch_delete_btn.setEnabled(has_selection)

    def filter_library(self, text):
        """Filter the library list based on search text."""
        text = text.lower()
        for i in range(self.library_list.count()):
            item = self.library_list.item(i)
            if item:
                item.setHidden(text not in item.text().lower())

    def show_library_context_menu(self, pos):
        """Show context menu for the library list."""
        item = self.library_list.itemAt(pos)
        if not item:
            return

        menu = QMenu()
        play_action = menu.addAction("▶️ Play")
        queue_action = menu.addAction("➕ Add to Queue")
        delete_action = menu.addAction("🗑️ Delete")

        action = menu.exec(self.library_list.mapToGlobal(pos))

        if action == play_action:
            self.log_output.append("Play functionality will be implemented soon.")
        elif action == queue_action:
            self.log_output.append("Queue functionality will be implemented soon.")
        elif action == delete_action:
            self.log_output.append("Delete functionality will be implemented soon.")

    def show_queue_context_menu(self, pos):
        """Show context menu for the queue list."""
        self.log_output.append("Queue context menu will be implemented soon.")

    def update_batch_delete_button(self):
        """Update the batch delete button state."""
        if hasattr(self, "batch_delete_btn"):
            # Enable if there are selected songs
            has_selection = (
                hasattr(self, "selected_songs") and len(self.selected_songs) > 0
            )
            self.batch_delete_btn.setEnabled(has_selection)

    def clear_queue(self):
        """Clear the queue."""
        self.current_queue.clear()
        self.current_queue_index = -1
        if hasattr(self, "queue_list"):
            self.queue_list.clear()
        self.log_output.append("Queue cleared.")

    def shuffle_queue(self):
        """Shuffle the queue."""
        if self.current_queue:
            random.shuffle(self.current_queue)
            self.refresh_queue_display()
            self.log_output.append("Queue shuffled.")

    def refresh_queue_display(self):
        """Refresh the queue display."""
        if hasattr(self, "queue_list"):
            self.queue_list.clear()
            for unique_id in self.current_queue:
                if unique_id in self.metadata:
                    title = self.metadata[unique_id]["title"]
                    item = QListWidgetItem(title)
                    item.setData(Qt.ItemDataRole.UserRole, unique_id)
                    self.queue_list.addItem(item)

            # Update queue status
            if hasattr(self, "queue_status_label"):
                count = len(self.current_queue)
                self.queue_status_label.setText(
                    f"Queue: {count} song{'s' if count != 1 else ''}"
                )

    def add_selected_to_queue(self):
        selected_items = self.library_list.selectedItems()
        for item in selected_items:
            data = item.data(Qt.ItemDataRole.UserRole)
            if data:
                # Extract unique_id from the tuple data
                unique_id = (
                    data[3] if isinstance(data, tuple) and len(data) > 3 else data
                )
                self.add_to_queue(unique_id)

    def add_to_queue(self, unique_id):
        """Add a song to the queue."""
        if unique_id not in self.current_queue:
            self.current_queue.append(unique_id)
            self.refresh_queue_display()
            if unique_id in self.metadata:
                title = self.metadata[unique_id]["title"]
                self.log_output.append(f"Added '{title}' to queue.")

    def remove_from_queue(self, index):
        """Remove a song from the queue by index."""
        if 0 <= index < len(self.current_queue):
            removed_id = self.current_queue.pop(index)
            if self.current_queue_index >= index:
                self.current_queue_index -= 1
            self.refresh_queue_display()
            if removed_id in self.metadata:
                self.log_output.append(f"Removed from queue: {self.metadata[removed_id]['title']}")

    def refresh_queue_display(self):
        """Refresh the queue display after changes."""
        self.queue_list.clear()
        for i, unique_id in enumerate(self.current_queue):
            if unique_id in self.metadata:
                title = self.metadata[unique_id]["title"]
                display_text = f"{i+1}. {title}"
                if i == self.current_queue_index:
                    display_text += " ⏵"  # Mark currently playing

                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, unique_id)
                self.queue_list.addItem(item)
            else:
                # Handle case where metadata might be missing for some reason
                item = QListWidgetItem(f"{i+1}. Unknown Song (ID: {unique_id})")
                item.setData(Qt.ItemDataRole.UserRole, unique_id)
                self.queue_list.addItem(item)

        self.update_queue_status()

    def update_queue_status(self):
        """Update the status label for the queue."""
        count = len(self.current_queue)
        if count == 0:
            self.queue_status_label.setText("Queue: empty")
        elif count == 1:
            self.queue_status_label.setText("Queue: 1 song")
        else:
            self.queue_status_label.setText(f"Queue: {count} songs")

    def show_queue_context_menu(self, position):
        """Show context menu for queue items."""
        item = self.queue_list.itemAt(position)
        if item is None:
            return

        menu = QMenu(self)

        play_action = QAction("▶️ Play Now", self)
        play_action.triggered.connect(lambda: self.play_from_queue(item))
        menu.addAction(play_action)

        remove_action = QAction("❌ Remove from Queue", self)
        remove_action.triggered.connect(
            lambda: self.remove_from_queue(self.queue_list.row(item))
        )
        menu.addAction(remove_action)

        move_up_action = QAction("🔼 Move Up", self)
        move_up_action.triggered.connect(lambda: self.move_queue_item(self.queue_list.row(item), -1))
        menu.addAction(move_up_action)

        move_down_action = QAction("🔽 Move Down", self)
        move_down_action.triggered.connect(lambda: self.move_queue_item(self.queue_list.row(item), 1))
        menu.addAction(move_down_action)

        # Disable move actions if not applicable
        selected_row = self.queue_list.row(item)
        if selected_row == 0:
            move_up_action.setEnabled(False)
        if selected_row == len(self.current_queue) - 1:
            move_down_action.setEnabled(False)

        # Show menu at cursor position
        global_pos = self.queue_list.mapToGlobal(position)
        menu.exec(global_pos)

    def play_from_queue(self, item):
        """Play the selected item from the queue."""
        unique_id = item.data(Qt.ItemDataRole.UserRole)
        if unique_id in self.current_queue:
            self.current_queue_index = self.current_queue.index(unique_id)
            self.load_song_for_playback(unique_id, play_after_load=True)
            self.refresh_queue_display()  # Update highlighting

    def move_queue_item(self, index, direction):
        """Move a queue item up or down."""
        new_index = index + direction
        if new_index < 0 or new_index >= len(self.current_queue):
            return

        # Swap the items in the list
        self.current_queue[index], self.current_queue[new_index] = (
            self.current_queue[new_index],
            self.current_queue[index],
        )

        # If the currently playing song was moved, update its index
        if self.current_queue_index == index:
            self.current_queue_index = new_index
        elif self.current_queue_index == new_index:
            self.current_queue_index = index

        # Refresh the queue display
        self.refresh_queue_display()

    def play_next_in_queue(self):
        """Play the next song in the queue."""
        if not self.current_queue or self.current_queue_index == -1:
            return

        self.current_queue_index += 1
        if self.current_queue_index >= len(self.current_queue):
            self.current_queue_index = -1  # End of queue
            self.log_output.append("Queue finished.")
            self.refresh_queue_display()
            self.stop_audio()
            return

        next_id = self.current_queue[self.current_queue_index]
        self.load_song_for_playback(next_id, play_after_load=True)

    def play_next_track(self):
        """Play the next track in the queue (for overlay controls)."""
        if not self.current_queue:
            self.log_output.append("No queue available for next track.")
            return

        if self.current_queue_index < 0:
            # No current track, start from beginning
            self.current_queue_index = 0
        else:
            # Move to next track
            self.current_queue_index += 1
            if self.current_queue_index >= len(self.current_queue):
                self.current_queue_index = 0  # Loop back to start

        next_id = self.current_queue[self.current_queue_index]
        self.load_song_for_playback(next_id, play_after_load=True)

    def play_previous_track(self):
        """Play the previous track in the queue (for overlay controls)."""
        if not self.current_queue:
            self.log_output.append("No queue available for previous track.")
            return

        if self.current_queue_index < 0:
            # No current track, start from end
            self.current_queue_index = len(self.current_queue) - 1
        else:
            # Move to previous track
            self.current_queue_index -= 1
            if self.current_queue_index < 0:
                self.current_queue_index = len(self.current_queue) - 1  # Loop to end

        prev_id = self.current_queue[self.current_queue_index]
        self.load_song_for_playback(prev_id, play_after_load=True)

    def load_song_for_playback(self, unique_id, play_after_load=False):
        print(f"=== LOADING SONG FOR PLAYBACK ===", flush=True)
        print(f"Unique ID: {unique_id}", flush=True)
        print(f"Play after load: {play_after_load}", flush=True)
        print(f"Use realtime audio: {self.use_realtime_audio}", flush=True)
        
        self.stop_audio()
        if unique_id not in self.metadata:
            self.log_output.append(f"Error: Song ID {unique_id} not found.")
            return

        files = get_song_files(unique_id)
        print(f"Video file: {files['video']}", flush=True)
        print(f"Vocals file: {files['vocals']}", flush=True)
        print(f"Instrumental file: {files['instrumental']}", flush=True)
        
        if not all(
            os.path.exists(f)
            for f in [files["video"], files["vocals"], files["instrumental"]]
        ):
            self.log_output.append(
                f"Error: Missing files for song '{self.metadata[unique_id]['title']}'."
            )
            self.enable_playback_controls(False)
            return

        self.current_unique_id = unique_id
        self.current_video_file = files["video"]
        self.current_vocals_file = files["vocals"]
        self.current_instrumental_file = files["instrumental"]
        meta = self.metadata[unique_id]
        self.title_label.setText(meta.get("title", "Unknown"))
        self.key_label.setText(f"Key: {meta.get('key', '-')}")
        self.bpm_label.setText(f"BPM: {meta.get('bpm', 0.0):.1f}")
        self.reset_effects()

        if self.use_realtime_audio:
            print("Loading for realtime audio mode", flush=True)
            if self.realtime_streamer.load_audio_files(
                self.current_vocals_file, self.current_instrumental_file
            ):
                self.total_length_seconds = self.realtime_streamer.get_duration()
                print(f"Audio duration: {self.total_length_seconds} seconds", flush=True)
                self.track_position_slider.setRange(0, int(self.total_length_seconds))
                print(f"Setting video source: {self.current_video_file}", flush=True)
                self.media_player.setSource(QUrl.fromLocalFile(self.current_video_file))
                
                # MUTE THE VIDEO AUDIO - only show video, play processed audio separately
                print("Muting video player audio to avoid double audio", flush=True)
                # Use the CrossfadePlayer's audio outputs to mute the video audio
                if hasattr(self.media_player, 'out1') and self.media_player.out1:
                    self.media_player.out1.setVolume(0.0)  # Mute primary audio output
                if hasattr(self.media_player, 'out2') and self.media_player.out2:
                    self.media_player.out2.setVolume(0.0)  # Mute secondary audio output
                print("Video audio muted successfully", flush=True)
                
                self.enable_playback_controls(True)
                self.on_slider_changed()  # Apply initial slider values
                print("Realtime audio setup complete", flush=True)
            else:
                self.log_output.append("Failed to load audio for real-time mode.")
                return
        else:
            print("Loading for mixed audio mode", flush=True)
            self.apply_effects_and_mix_auto()

        if play_after_load:
            QTimer.singleShot(200, self.toggle_play_pause)
        self.refresh_queue_display()

    def update_log(self, message):
        """Update the log output with detailed debugging."""
        print(f"LOG: {message}", flush=True)  # Also print to console for debugging
        if hasattr(self, "log_output") and self.log_output:
            self.log_output.append(message)
        else:
            print(f"WARNING: log_output not available, message was: {message}", flush=True)

    def update_progress(self, progress, status):
        """Update the progress dialog and log with enhanced error handling."""
        # Always log the progress to the status tab for visibility
        log_message = f"Progress {progress}%: {status}"
        print(f"PROGRESS: {log_message}", flush=True)  # Console logging
        self.update_log(log_message)
        
        # Update progress bar in the main UI
        if hasattr(self, "progress_bar") and self.progress_bar:
            try:
                self.progress_bar.setVisible(True)
                if progress >= 0:
                    self.progress_bar.setRange(0, 100)
                    self.progress_bar.setValue(progress)
                else:
                    self.progress_bar.setRange(0, 0)  # Indeterminate
            except Exception as e:
                print(f"ERROR updating progress bar: {e}", flush=True)
        
        # Update progress dialog only if it exists and is valid
        try:
            if hasattr(self, "progress_dialog") and self.progress_dialog is not None:
                if not self.progress_dialog.wasCanceled():
                    self.progress_dialog.setValue(progress)
                    self.progress_dialog.setLabelText(status)
                else:
                    print("Progress dialog was cancelled, skipping update", flush=True)
            else:
                print(f"Progress dialog not available (None or missing attribute)", flush=True)
        except RuntimeError as e:
            print(f"RuntimeError updating progress dialog (widget deleted?): {e}", flush=True)
            # Clear the reference if the widget was deleted
            self.progress_dialog = None
        except Exception as e:
            print(f"Unexpected error updating progress dialog: {e}", flush=True)
            self.progress_dialog = None

    def enable_playback_controls(self, enabled=True):
        """Enable or disable playback controls."""
        print(f"Enabling playback controls: {enabled}", flush=True)
        if hasattr(self, "play_button"):
            self.play_button.setEnabled(enabled)
        if hasattr(self, "pause_button"):
            self.pause_button.setEnabled(enabled)
        if hasattr(self, "stop_button"):
            self.stop_button.setEnabled(enabled)
        
        # Also enable overlay controls
        if hasattr(self, "overlay_play_button"):
            self.overlay_play_button.setEnabled(enabled)
        if hasattr(self, "overlay_pause_button"):
            self.overlay_pause_button.setEnabled(enabled)
        if hasattr(self, "overlay_stop_button"):
            self.overlay_stop_button.setEnabled(enabled)
        
        # Enable position sliders
        if hasattr(self, "track_position_slider"):
            self.track_position_slider.setEnabled(enabled)
        if hasattr(self, "overlay_position_slider"):
            self.overlay_position_slider.setEnabled(enabled)

    def reset_sliders_to_default(self):
        """Reset all sliders to default values."""
        if hasattr(self, "vocal_volume_slider"):
            self.vocal_volume_slider.setValue(100)
        if hasattr(self, "instrumental_volume_slider"):
            self.instrumental_volume_slider.setValue(100)
        if hasattr(self, "vocal_pitch_slider"):
            self.vocal_pitch_slider.setValue(0)
        if hasattr(self, "instrumental_pitch_slider"):
            self.instrumental_pitch_slider.setValue(0)

    def migrate_legacy_files(self):
        """Migrate legacy files to new format."""
        # Placeholder for legacy file migration
        pass

    def play_next_in_queue(self):
        """Play the next song in the queue."""
        if not self.current_queue or self.current_queue_index == -1:
            return

        self.current_queue_index += 1
        if self.current_queue_index >= len(self.current_queue):
            self.current_queue_index = -1  # End of queue
            self.log_output.append("Queue finished.")
            self.refresh_queue_display()
            self.stop_audio()
            return

        next_id = self.current_queue[self.current_queue_index]
        self.load_song_for_playback(next_id, play_after_load=True)

    def update_playback_position(self):
        """Update the playback position display."""
        pos_str = self.format_time(self.current_position_seconds)
        total_str = self.format_time(self.total_length_seconds)
        self.playback_position_label.setText(f"{pos_str} / {total_str}")

        # Also update overlay label
        if hasattr(self, "overlay_time_label"):
            self.overlay_time_label.setText(f"{pos_str} / {total_str}")

    def update_realtime_position(self):
        """Update position from the real-time streamer."""
        if self.is_playing:
            self.current_position_seconds = self.realtime_streamer.get_position()
            self.track_position_slider.setValue(int(self.current_position_seconds))
            self.update_playback_position()

            # Check for end of track
            if self.current_position_seconds >= self.total_length_seconds - 0.1:
                self.stop_audio()
                # Auto-play next in queue
                if self.current_queue_index != -1:
                    self.play_next_in_queue()

    def format_time(self, seconds):
        """Format seconds into MM:SS string."""
        if seconds < 0:
            seconds = 0
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"

    def on_queue_item_clicked(self, item):
        """Handle clicks on queue items."""
        # This is now handled by the context menu's "Play Now" action
        pass

    def on_library_item_clicked(self, item):
        """Handle library item clicks - selection or playback based on modifier keys."""
        from PyQt6.QtGui import QGuiApplication

        data = item.data(Qt.ItemDataRole.UserRole)
        if not data:
            return

        # This unpacking will now work correctly
        video_path, vocals_path, instrumental_path, unique_id = data

        modifiers = QGuiApplication.keyboardModifiers()
        if modifiers & Qt.KeyboardModifier.ControlModifier:
            self.toggle_song_selection(unique_id, item)
            self.update_selection_buttons() # Update buttons on selection change
            return

        # If not Ctrl-click, load and play the song
        self.load_song_for_playback(unique_id, play_after_load=True)

        # Reset sliders for new song
        self.reset_sliders_to_default()

    def on_library_item_double_clicked(self, item):
        """Handle double-clicks on library items - directly play the song."""
        data = item.data(Qt.ItemDataRole.UserRole)
        if not data:
            return

        video_path, vocals_path, instrumental_path, unique_id = data
        self.load_song_for_playback(unique_id, play_after_load=True)

    # --- Queue Management ---

    def clear_queue(self):
        """Clear all songs from the queue."""
        self.queue_list.clear()
        self.current_queue = []
        self.current_queue_index = -1
        self.update_queue_status()
        self.log_output.append("Queue cleared")

    def shuffle_queue(self):
        """Shuffle the current queue."""
        if len(self.current_queue) <= 1:
            return

        import random

        # Keep track of currently playing song
        current_song = None
        if (
            self.current_queue_index >= 0
            and self.current_queue_index < self.current_queue
        ):
            current_song = self.current_queue[self.current_queue_index]

        # Shuffle the queue
        random.shuffle(self.current_queue)

        # Update queue index if there was a current song
        if current_song:
            try:
                self.current_queue_index = self.current_queue.index(current_song)
            except ValueError:
                self.current_queue_index = -1

        # Refresh the queue display
        self.refresh_queue_display()
        self.log_output.append("Queue shuffled")

    def remove_from_queue(self, index):
        """Remove a song from the queue by index."""
        if 0 <= index < len(self.current_queue):
            removed_id = self.current_queue.pop(index)
            if self.current_queue_index >= index:
                self.current_queue_index -= 1
            self.refresh_queue_display()
            if removed_id in self.metadata:
                self.log_output.append(f"Removed from queue: {self.metadata[removed_id]['title']}")

    def refresh_queue_display(self):
        """Refresh the queue display after changes."""
        self.queue_list.clear()
        for i, unique_id in enumerate(self.current_queue):
            if unique_id in self.metadata:
                title = self.metadata[unique_id]["title"]
                display_text = f"{i+1}. {title}"
                if i == self.current_queue_index:
                    display_text += " ⏵"  # Mark currently playing

                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, unique_id)
                self.queue_list.addItem(item)
            else:
                # Handle case where metadata might be missing for some reason
                item = QListWidgetItem(f"{i+1}. Unknown Song (ID: {unique_id})")
                item.setData(Qt.ItemDataRole.UserRole, unique_id)
                self.queue_list.addItem(item)

        self.update_queue_status()

    def update_queue_status(self):
        """Update the status label for the queue."""
        count = len(self.current_queue)
        if count == 0:
            self.queue_status_label.setText("Queue: empty")
        elif count == 1:
            self.queue_status_label.setText("Queue: 1 song")
        else:
            self.queue_status_label.setText(f"Queue: {count} songs")

    def show_queue_context_menu(self, position):
        """Show context menu for queue items."""
        item = self.queue_list.itemAt(position)
        if item is None:
            return

        menu = QMenu(self)

        play_action = QAction("▶️ Play Now", self)
        play_action.triggered.connect(lambda: self.play_from_queue(item))
        menu.addAction(play_action)

        remove_action = QAction("❌ Remove from Queue", self)
        remove_action.triggered.connect(
            lambda: self.remove_from_queue(self.queue_list.row(item))
        )
        menu.addAction(remove_action)

        move_up_action = QAction("🔼 Move Up", self)
        move_up_action.triggered.connect(lambda: self.move_queue_item(self.queue_list.row(item), -1))
        menu.addAction(move_up_action)

        move_down_action = QAction("🔽 Move Down", self)
        move_down_action.triggered.connect(lambda: self.move_queue_item(self.queue_list.row(item), 1))
        menu.addAction(move_down_action)

        # Disable move actions if not applicable
        selected_row = self.queue_list.row(item)
        if selected_row == 0:
            move_up_action.setEnabled(False)
        if selected_row == len(self.current_queue) - 1:
            move_down_action.setEnabled(False)

        # Show menu at cursor position
        global_pos = self.queue_list.mapToGlobal(position)
        menu.exec(global_pos)

    def play_from_queue(self, item):
        """Play the selected item from the queue."""
        unique_id = item.data(Qt.ItemDataRole.UserRole)
        if unique_id in self.current_queue:
            self.current_queue_index = self.current_queue.index(unique_id)
            self.load_song_for_playback(unique_id, play_after_load=True)
            self.refresh_queue_display()  # Update highlighting

    def move_queue_item(self, index, direction):
        """Move a queue item up or down."""
        new_index = index + direction
        if new_index < 0 or new_index >= len(self.current_queue):
            return

        # Swap the items in the list
        self.current_queue[index], self.current_queue[new_index] = (
            self.current_queue[new_index],
            self.current_queue[index],
        )

        # If the currently playing song was moved, update its index
        if self.current_queue_index == index:
            self.current_queue_index = new_index
        elif self.current_queue_index == new_index:
            self.current_queue_index = index

        # Refresh the queue display
        self.refresh_queue_display()

    # --- Audio Effects and Mixing ---
    def on_slider_changed(self):
        """Handle value changes from any of the effects sliders."""
        # Update labels immediately
        self.vocal_volume_label.setText(f"{self.vocal_volume_slider.value()}%")
        self.instrumental_volume_label.setText(
            f"{self.instrumental_volume_slider.value()}%"
        )
        self.vocal_pitch_label.setText(f"{self.vocal_pitch_slider.value()} ♪")
        self.instrumental_pitch_label.setText(
            f"{self.instrumental_pitch_slider.value()} ♪"
        )

        # If in real-time mode, apply effects instantly
        if self.use_realtime_audio:
            self.realtime_streamer.set_volumes(
                self.vocal_volume_slider.value(),
                self.instrumental_volume_slider.value(),
            )
            self.realtime_streamer.set_pitch_shifts(
                self.vocal_pitch_slider.value(), self.instrumental_pitch_slider.value()
            )
        else:
            # In pre-mixed mode, start a timer to apply changes after a short delay
            # This prevents re-mixing on every single slider tick
            self.slider_change_timer.start(500)

    def on_crossfade_duration_changed(self, value):
        """Handle crossfade duration slider change."""
        self.fade_duration = value
        self.crossfade_label.setText(f"{value}s")
        self.media_player.setFadeDuration(value)
        self.preferences["fade_duration"] = value
        # No need to call save_preferences here, it's done on close

    def apply_effects_and_mix_auto(self, play_after_load=False):
        """Automatically apply effects and mix audio. Can be triggered by sliders or song load."""
        if self.mixing_thread and self.mixing_thread.isRunning():
            self.log_output.append("Mixing is already in progress. Please wait.")
            return

        if self.use_realtime_audio:
            # In real-time mode, this function's purpose is just to ensure playback starts
            if play_after_load and not self.is_playing:
                self.toggle_play_pause()
            return

        if not self.current_vocals_file or not self.current_instrumental_file:
            return

        # Show indeterminate progress bar during mixing
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # Get current playback state to restore it after mixing
        was_playing = (
            self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState
        )
        position_to_seek = self.media_player.position()

        # Define output paths
        mixed_wav_path = os.path.join(MIXED_DIR, f"{self.current_unique_id}_mixed.wav")
        remux_mp4_path = os.path.join(
            REMUX_DIR, f"{self.current_unique_id}_karaoke.mp4"
        )

        # Start mixing thread
        self.mixing_thread = MixingThread(
            self.current_vocals_file,
            self.current_instrumental_file,
            self.vocal_volume_slider.value(),
            self.instrumental_volume_slider.value(),
            self.vocal_pitch_slider.value(),
            self.instrumental_pitch_slider.value(),
            mixed_wav_path,
            self.current_video_file,
            remux_mp4_path,
        )

        # Store playback state in the thread object
        self.mixing_thread.was_playing = was_playing
        self.mixing_thread.position_to_seek = position_to_seek

        self.mixing_thread.log_message.connect(self.log_output.append)
        self.mixing_thread.finished_mixing.connect(self.on_mixing_finished)
        self.mixing_thread.start()

    def on_mixing_finished(self, mixed_wav_path, remuxed_mp4_path):
        """Handle completion of the mixing thread."""
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 100)

        if mixed_wav_path and remuxed_mp4_path:
            self.current_mixed_wav = mixed_wav_path
            self.current_remuxed_mp4 = remuxed_mp4_path

            # Load the new remuxed video into the player
            self.media_player.setSource(QUrl.fromLocalFile(self.current_remuxed_mp4))
            self.enable_playback_controls(True)

            # Restore playback state
            if self.mixing_thread.was_playing:
                self.media_player.play()
            if self.mixing_thread.position_to_seek > 0:
                self.media_player.setPosition(self.mixing_thread.position_to_seek)
        else:
            self.log_output.append("❌ Mixing or remuxing failed. Check logs.")
            self.enable_playback_controls(False)

        # Ensure the thread object is cleaned up
        if self.mixing_thread:
            self.mixing_thread.deleteLater()
            self.mixing_thread = None

    def reset_effects(self):
        """Reset all audio effect sliders to their default values."""
        self.vocal_volume_slider.setValue(100)
        self.instrumental_volume_slider.setValue(100)
        self.vocal_pitch_slider.setValue(0)
        self.instrumental_pitch_slider.setValue(0)

        # Trigger a mix if not in real-time mode
        if not self.use_realtime_audio:
            self.slider_change_timer.start(100)

    # --- Playback Control ---
    def toggle_play_pause(self):
        """Toggle play/pause for the current audio mode."""
        print(f"=== TOGGLE PLAY/PAUSE ===", flush=True)
        print(f"Use realtime audio: {self.use_realtime_audio}", flush=True)
        print(f"Current playing state: {self.is_playing}", flush=True)
        
        if self.use_realtime_audio:
            if self.is_playing:
                print("Pausing realtime audio", flush=True)
                self.realtime_streamer.pause_playback()
                self.media_player.pause()  # Pause video
                self.update_play_button_icon(False)
                self.is_playing = False
                self.realtime_position_timer.stop()
            else:
                print("Starting realtime audio", flush=True)
                if self.realtime_streamer.start_playback():
                    self.media_player.play()  # Play video
                    self.update_play_button_icon(True)
                    self.is_playing = True
                    self.realtime_position_timer.start(
                        100
                    )  # Update position every 100ms
                    print("Realtime audio started successfully", flush=True)
                else:
                    print("Failed to start realtime audio", flush=True)
        else:
            print("Using media player mode", flush=True)
            current_state = self.media_player.playbackState()
            print(f"Current media player state: {current_state}", flush=True)
            if current_state == QMediaPlayer.PlaybackState.PlayingState:
                print("Pausing media player", flush=True)
                self.media_player.pause()
                self.update_play_button_icon(False)
                self.is_playing = False
            else:
                print("Starting media player", flush=True)
                self.media_player.play()
                self.update_play_button_icon(True)
                self.is_playing = True

    def pause_audio(self):
        """Pause audio playback."""
        if self.use_realtime_audio:
            self.realtime_streamer.pause_playback()
            self.update_play_button_icon(False)
            self.is_playing = False
        else:
            self.media_player.pause()

    def stop_audio(self):
        """Stop audio playback and reset position."""
        self.system_initiated_stop = True  # Prevent queue advancement on manual stop
        if self.use_realtime_audio:
            self.realtime_streamer.stop_playback()
            self.media_player.stop()
            self.is_playing = False
            self.realtime_position_timer.stop()
            self.update_realtime_position()  # Final update to show 00:00
        else:
            self.media_player.stop()

        self.update_play_button_icon(False)
        self.track_position_slider.setValue(0)
        self.current_position_seconds = 0
        self.playback_position_label.setText(
            f"00:00 / {self.format_time(self.total_length_seconds)}"
        )
        self.system_initiated_stop = False  # Reset flag after stop

    def on_media_position_changed(self, position):
        """Handle position change from QMediaPlayer (pre-mixed mode)."""
        if not self.track_slider_pressed:
            self.current_position_seconds = position / 1000.0
            self.track_position_slider.setValue(int(self.current_position_seconds))
            # Also update overlay slider
            if hasattr(self, "overlay_position_slider"):
                self.overlay_position_slider.setValue(int(self.current_position_seconds))
            self.update_playback_position()

    def on_media_duration_changed(self, duration):
        """Handle duration change from QMediaPlayer."""
        print(f"Media duration changed: {duration} ms ({duration/1000.0} seconds)", flush=True)
        if not self.use_realtime_audio:
            self.total_length_seconds = duration / 1000.0
            self.track_position_slider.setRange(0, int(self.total_length_seconds))
            # Also update overlay slider range
            if hasattr(self, "overlay_position_slider"):
                self.overlay_position_slider.setRange(0, int(self.total_length_seconds))
                print(f"Updated overlay slider range to: 0-{int(self.total_length_seconds)}", flush=True)
            self.duration_label.setText(
                f"⏱ {self.format_time(self.total_length_seconds)}"
            )
            self.update_playback_position()

    def on_playback_state_changed(self, state):
        """Handle playback state changes from QMediaPlayer."""
        print(f"=== PLAYBACK STATE CHANGED ===", flush=True)
        print(f"New state: {state}", flush=True)
        
        if state == QMediaPlayer.PlaybackState.PlayingState:
            print("State: PLAYING", flush=True)
            self.is_playing = True
            self.update_play_button_icon(True)
            self.playback_timer.start(100)
            # Also update overlay controls to show playing state
            if hasattr(self, "overlay_play_button"):
                self.overlay_play_button.setText("⏸")  # Show pause icon when playing
        elif state == QMediaPlayer.PlaybackState.PausedState:
            print("State: PAUSED", flush=True)
            self.is_playing = False
            self.update_play_button_icon(False)
            self.playback_timer.stop()
            # Also update overlay controls to show paused state
            if hasattr(self, "overlay_play_button"):
                self.overlay_play_button.setText("▶")  # Show play icon when paused
        elif state == QMediaPlayer.PlaybackState.StoppedState:
            print("State: STOPPED", flush=True)
            self.is_playing = False
            self.update_play_button_icon(False)
            self.playback_timer.stop()
            self.current_position_seconds = 0
            self.track_position_slider.setValue(0)
            # Also update overlay slider
            if hasattr(self, "overlay_position_slider"):
                self.overlay_position_slider.setValue(0)
            self.update_playback_position()
            # Also update overlay controls to show stopped state
            if hasattr(self, "overlay_play_button"):
                self.overlay_play_button.setText("▶")  # Show play icon when stopped

            # Auto-play next song in queue if one exists
            if (
                self.current_queue_index != -1
                and not self.crossfade_in_progress
                and not self.system_initiated_stop
            ):
                print("Auto-playing next song in queue", flush=True)
                self.play_next_in_queue()
            else:
                print("Not auto-playing next song", flush=True)
                print(f"  Queue index: {self.current_queue_index}", flush=True)
                print(f"  Crossfade in progress: {getattr(self, 'crossfade_in_progress', 'not set')}", flush=True)
                print(f"  System initiated stop: {getattr(self, 'system_initiated_stop', 'not set')}", flush=True)

    def update_playback_position(self):
        """Update the playback position label and slider."""
        pos_str = self.format_time(self.current_position_seconds)
        total_str = self.format_time(self.total_length_seconds)
        time_text = f"{pos_str} / {total_str}"
        
        self.playback_position_label.setText(time_text)

        # Also update overlay label
        if hasattr(self, "overlay_time_label"):
            self.overlay_time_label.setText(time_text)
            print(f"Updated overlay time label: {time_text}", flush=True)

    def update_realtime_position(self):
        """Update position from the real-time streamer."""
        if self.is_playing:
            self.current_position_seconds = self.realtime_streamer.get_position()
            self.track_position_slider.setValue(int(self.current_position_seconds))
            self.update_playback_position()

            # Check for end of track
            if self.current_position_seconds >= self.total_length_seconds - 0.1:
                self.stop_audio()
                # Auto-play next in queue
                if self.current_queue_index != -1:
                    self.play_next_in_queue()

    def on_track_slider_pressed(self):
        """Handle when the user presses the track position slider."""
        self.track_slider_pressed = True

    def on_track_slider_released(self):
        """Handle when the user releases the track position slider."""
        self.track_slider_pressed = False
        new_pos_sec = self.track_position_slider.value()
        if self.use_realtime_audio:
            self.realtime_streamer.seek(new_pos_sec)
            self.media_player.setPosition(int(new_pos_sec * 1000))  # Sync video
        else:
            self.media_player.setPosition(int(new_pos_sec * 1000))

    def on_track_slider_moved(self, position):
        """Update the time label as the user drags the slider."""
        self.current_position_seconds = position
        self.update_playback_position()

    def on_crossfade_finished(self):
        """Called when the crossfade completes."""
        self.crossfade_in_progress = False
        self.log_output.append("Crossfade complete.")
        # The new track is now the current one, update UI
        self.load_song_for_playback(self.next_item["unique_id"], play_after_load=True)
        self.next_item = None

    def format_time(self, seconds):
        """Format seconds into a MM:SS string."""
        mins = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{mins:02d}:{secs:02d}"

    def eventFilter(self, source, event):
        """Filter events to show/hide video overlay."""
        if source == self.video_container:
            if event.type() == event.Type.Enter:
                if hasattr(self, "overlay_widget"):
                    self.overlay_widget.show()
                    self.overlay_widget.raise_()  # Ensure it's on top
                return True
            elif event.type() == event.Type.Leave:
                # Don't auto-hide overlay to ensure it stays visible
                # User can see controls at all times
                if hasattr(self, "overlay_widget"):
                    self.overlay_widget.show()  # Keep overlay visible
                    self.overlay_widget.raise_()  # Ensure it's on top
                return True
            elif event.type() == event.Type.MouseMove:
                if hasattr(self, "overlay_widget"):
                    self.overlay_widget.show()
                    self.overlay_widget.raise_()  # Ensure it's on top
                return True
        return super().eventFilter(source, event)


def main():
    # Handle high DPI scaling
    if hasattr(Qt, "AA_EnableHighDpiScaling"):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, "AA_UseHighDpiPixmaps"):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Ensure atexit runs our cleanup
    signal.signal(signal.SIGINT, signal.SIG_DFL)

    print("Creating QApplication...")
    app = QApplication(sys.argv)

    # Set a default style for consistency
    app.setStyle(QStyleFactory.create("Fusion"))

    print("Creating KaraokeApp...")
    ex = KaraokeApp()
    print("Showing window...")
    ex.show()
    print("Window should be visible now...")

    # Graceful exit
    try:
        print("Starting event loop...")
        exit_code = app.exec()
        print(f"Event loop ended with code: {exit_code}")
        sys.exit(exit_code)
    except SystemExit:
        print("Closing application.")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
